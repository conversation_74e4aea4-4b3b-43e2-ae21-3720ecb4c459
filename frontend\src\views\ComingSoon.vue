<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <div>
        <div class="mx-auto h-16 w-16 bg-red-600 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-2xl">G</span>
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Coming Soon
        </h2>
        <p class="mt-2 text-lg text-gray-600">
          This feature is currently under development
        </p>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">We're working on it!</h3>
        <p class="text-sm text-gray-500 mb-4">
          This feature will be available in a future update. Stay tuned for more exciting features coming to Gignify.
        </p>
        <div class="space-y-2">
          <div class="text-sm text-gray-600">
            <strong>What's coming:</strong>
          </div>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• Milestone-based payments</li>
            <li>• Advanced project management</li>
            <li>• Video chat integration</li>
            <li>• Enhanced portfolio features</li>
          </ul>
        </div>
      </div>

      <div class="flex justify-center space-x-4">
        <router-link
          to="/"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
        >
          Back to Home
        </router-link>
        <button
          @click="$router.go(-1)"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          Go Back
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Coming soon page for features under development
</script>
