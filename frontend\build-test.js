#!/usr/bin/env node

// Simple build test script
import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

console.log('🔧 Testing build process...');

try {
  // Clean previous build
  console.log('📦 Installing dependencies...');
  execSync('npm ci', { stdio: 'inherit' });

  // Run build
  console.log('🏗️  Building project...');
  execSync('npm run build', { stdio: 'inherit' });

  // Check if dist folder exists
  const distPath = join(process.cwd(), 'dist');
  if (existsSync(distPath)) {
    console.log('✅ Build successful! Dist folder created.');
    
    // Check for key files
    const indexPath = join(distPath, 'index.html');
    if (existsSync(indexPath)) {
      console.log('✅ index.html found in dist folder.');
    } else {
      console.log('❌ index.html not found in dist folder.');
      process.exit(1);
    }
  } else {
    console.log('❌ Build failed! Dist folder not created.');
    process.exit(1);
  }

  console.log('🎉 All tests passed! Build is ready for deployment.');
} catch (error) {
  console.error('❌ Build test failed:', error.message);
  process.exit(1);
}
