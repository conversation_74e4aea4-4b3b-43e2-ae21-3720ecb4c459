import { validators, sanitizers, spamDetection, RateLimiter } from '../utils/validation'

// Security service for the application
export class SecurityService {
  constructor() {
    // Rate limiters for different actions
    this.rateLimiters = {
      login: new RateLimiter(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
      signup: new RateLimiter(3, 60 * 60 * 1000), // 3 attempts per hour
      jobPost: new RateLimiter(10, 60 * 60 * 1000), // 10 jobs per hour
      proposal: new RateLimiter(20, 60 * 60 * 1000), // 20 proposals per hour
      message: new RateLimiter(100, 60 * 60 * 1000) // 100 messages per hour
    }
  }

  // Check if action is rate limited
  checkRateLimit(action, userId) {
    const limiter = this.rateLimiters[action]
    if (!limiter) return { allowed: true }

    const allowed = limiter.isAllowed(userId)
    const remaining = limiter.getRemainingAttempts(userId)
    const resetTime = limiter.getTimeUntilReset(userId)

    return {
      allowed,
      remaining,
      resetTime: resetTime > 0 ? new Date(Date.now() + resetTime) : null
    }
  }

  // Validate and sanitize user registration data
  validateUserRegistration(data) {
    const errors = {}
    const sanitized = {}

    // Validate and sanitize name
    const nameError = validators.name(data.name)
    if (nameError) {
      errors.name = nameError
    } else {
      sanitized.name = sanitizers.text(data.name)
    }

    // Validate and sanitize email
    const emailError = validators.email(data.email)
    if (emailError) {
      errors.email = emailError
    } else {
      sanitized.email = sanitizers.email(data.email)
    }

    // Validate password
    const passwordError = validators.passwordSimple(data.password)
    if (passwordError) {
      errors.password = passwordError
    }

    // Validate role
    if (!data.role || !['client', 'freelancer'].includes(data.role)) {
      errors.role = 'Please select a valid role'
    } else {
      sanitized.role = data.role
    }

    // Check for spam patterns in name
    const spamCheck = spamDetection.checkText(data.name)
    if (spamCheck.isSpam) {
      errors.name = 'Name contains suspicious content'
    }

    // Check user behavior
    const behaviorCheck = spamDetection.checkUserBehavior(data)
    if (behaviorCheck.isSuspicious) {
      errors.general = 'Account creation flagged for review'
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      sanitized: { ...sanitized, password: data.password } // Don't sanitize password
    }
  }

  // Validate and sanitize job posting data
  validateJobPosting(data) {
    const errors = {}
    const sanitized = {}

    // Validate and sanitize title
    const titleError = validators.jobTitle(data.title)
    if (titleError) {
      errors.title = titleError
    } else {
      sanitized.title = sanitizers.text(data.title)
      
      // Check for spam in title
      const spamCheck = spamDetection.checkText(sanitized.title)
      if (spamCheck.isSpam) {
        errors.title = 'Job title contains suspicious content'
      }
    }

    // Validate and sanitize description
    const descError = validators.jobDescription(data.description)
    if (descError) {
      errors.description = descError
    } else {
      sanitized.description = sanitizers.text(data.description)
      
      // Check for spam in description
      const spamCheck = spamDetection.checkText(sanitized.description)
      if (spamCheck.isSpam) {
        errors.description = 'Job description contains suspicious content'
      }
    }

    // Validate budget
    const budgetError = validators.budget(data.budget)
    if (budgetError) {
      errors.budget = budgetError
    } else {
      sanitized.budget = sanitizers.number(data.budget)
    }

    // Validate category
    const validCategories = ['ai', 'dev', 'design', 'writing', 'marketing', 'video', 'music', 'business']
    if (!data.category || !validCategories.includes(data.category)) {
      errors.category = 'Please select a valid category'
    } else {
      sanitized.category = data.category
    }

    // Validate and sanitize skills
    if (data.skills) {
      const skillsError = validators.skills(data.skills)
      if (skillsError) {
        errors.skills = skillsError
      } else {
        sanitized.skills = sanitizers.skills(data.skills)
      }
    }

    // Validate deadline
    if (data.deadline) {
      const deadline = new Date(data.deadline)
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      
      if (deadline < tomorrow) {
        errors.deadline = 'Deadline must be at least 1 day from now'
      } else {
        sanitized.deadline = deadline
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      sanitized
    }
  }

  // Validate and sanitize proposal data
  validateProposal(data) {
    const errors = {}
    const sanitized = {}

    // Validate price
    const priceError = validators.budget(data.price)
    if (priceError) {
      errors.price = priceError.replace('Budget', 'Price')
    } else {
      sanitized.price = sanitizers.number(data.price)
    }

    // Validate and sanitize cover letter
    if (!data.coverLetter) {
      errors.coverLetter = 'Cover letter is required'
    } else if (data.coverLetter.length < 50) {
      errors.coverLetter = 'Cover letter must be at least 50 characters long'
    } else if (data.coverLetter.length > 2000) {
      errors.coverLetter = 'Cover letter must be less than 2000 characters'
    } else {
      sanitized.coverLetter = sanitizers.text(data.coverLetter)
      
      // Check for spam in cover letter
      const spamCheck = spamDetection.checkText(sanitized.coverLetter)
      if (spamCheck.isSpam) {
        errors.coverLetter = 'Cover letter contains suspicious content'
      }
    }

    // Validate delivery time
    if (!data.deliveryTime) {
      errors.deliveryTime = 'Delivery time is required'
    } else {
      const deliveryDays = parseInt(data.deliveryTime)
      if (isNaN(deliveryDays) || deliveryDays < 1 || deliveryDays > 30) {
        errors.deliveryTime = 'Delivery time must be between 1 and 30 days'
      } else {
        sanitized.deliveryTime = deliveryDays
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      sanitized
    }
  }

  // Validate and sanitize chat message
  validateMessage(data) {
    const errors = {}
    const sanitized = {}

    // Validate message content
    if (!data.message || !data.message.trim()) {
      errors.message = 'Message cannot be empty'
    } else if (data.message.length > 2000) {
      errors.message = 'Message must be less than 2000 characters'
    } else {
      sanitized.message = sanitizers.text(data.message)
      
      // Check for spam
      const spamCheck = spamDetection.checkText(sanitized.message)
      if (spamCheck.isSpam) {
        errors.message = 'Message contains suspicious content'
      }
    }

    // Validate attachments if present
    if (data.attachments && data.attachments.length > 0) {
      const maxFiles = 5
      const maxFileSize = 10 * 1024 * 1024 // 10MB
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain',
        'application/zip', 'application/x-zip-compressed'
      ]

      if (data.attachments.length > maxFiles) {
        errors.attachments = `Maximum ${maxFiles} files allowed`
      } else {
        for (const file of data.attachments) {
          const fileError = validators.file(file, { maxSize: maxFileSize, allowedTypes })
          if (fileError) {
            errors.attachments = fileError
            break
          }
        }
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      sanitized
    }
  }

  // Validate file uploads
  validateFileUpload(file, options = {}) {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = [],
      allowedExtensions = []
    } = options

    const errors = []

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`)
    }

    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      errors.push(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`)
    }

    // Check file extension
    if (allowedExtensions.length > 0) {
      const extension = file.name.split('.').pop().toLowerCase()
      if (!allowedExtensions.includes(extension)) {
        errors.push(`File extension not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`)
      }
    }

    // Check for suspicious file names
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|scr|pif|com)$/i,
      /script/i,
      /malware/i
    ]

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(file.name)) {
        errors.push('File name contains suspicious content')
        break
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Content moderation for user-generated content
  moderateContent(content, type = 'general') {
    const result = {
      approved: true,
      confidence: 0,
      flags: [],
      sanitized: sanitizers.text(content)
    }

    // Check for spam
    const spamCheck = spamDetection.checkText(content)
    if (spamCheck.isSpam) {
      result.approved = false
      result.confidence = spamCheck.confidence
      result.flags.push('spam')
    }

    // Check for inappropriate content (basic patterns)
    const inappropriatePatterns = [
      /\b(hate|racist|sexist|offensive)\b/i,
      /\b(scam|fraud|fake|cheat)\b/i
    ]

    for (const pattern of inappropriatePatterns) {
      if (pattern.test(content)) {
        result.approved = false
        result.flags.push('inappropriate')
        break
      }
    }

    // Type-specific checks
    if (type === 'job') {
      // Check for unrealistic budgets or requirements
      if (/\$\d{6,}/.test(content)) {
        result.flags.push('unrealistic_budget')
      }
    }

    return result
  }

  // Generate CSRF token (simple implementation)
  generateCSRFToken() {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  // Validate CSRF token
  validateCSRFToken(token, storedToken) {
    return token === storedToken
  }
}

// Create singleton instance
export const securityService = new SecurityService()

export default securityService
