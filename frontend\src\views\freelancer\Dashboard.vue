<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Freelancer Dashboard</h1>
        <p class="mt-2 text-gray-600">Welcome back, {{ userData?.name }}!</p>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-sm">XP</span>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Experience Points</h3>
              <p class="text-2xl font-bold text-yellow-500">{{ userData?.xp || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Tier Level</h3>
              <p class="text-2xl font-bold text-purple-600">{{ userData?.tier || 1 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Active Proposals</h3>
              <p class="text-2xl font-bold text-blue-600">{{ activeProposalsCount }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Earnings</h3>
              <p class="text-2xl font-bold text-green-600">${{ userData?.walletBalance || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Available Jobs -->
      <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 class="text-lg font-medium text-gray-900">Available Jobs</h2>
          <div class="flex space-x-2">
            <select
              v-model="selectedCategory"
              @change="filterJobs"
              class="border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
            >
              <option value="">All Categories</option>
              <option v-for="category in jobsStore.jobCategories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>
        
        <div v-if="loading" class="p-6 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
          <p class="mt-2 text-gray-500">Loading jobs...</p>
        </div>

        <div v-else-if="availableJobs.length === 0" class="p-6 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No jobs available</h3>
          <p class="mt-1 text-sm text-gray-500">Check back later for new opportunities.</p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="job in availableJobs.slice(0, 10)"
            :key="job.id"
            class="p-6 hover:bg-gray-50 cursor-pointer"
            @click="$router.push(`/freelancer/job/${job.id}`)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">{{ job.title }}</h3>
                <p class="mt-1 text-sm text-gray-500">{{ job.description?.substring(0, 150) }}...</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                  <span class="capitalize">{{ job.category }}</span>
                  <span>•</span>
                  <span>${{ job.budget }}</span>
                  <span>•</span>
                  <span>{{ Object.keys(job.proposals || {}).length }} proposals</span>
                  <span>•</span>
                  <span>{{ formatDate(job.createdAt) }}</span>
                </div>
              </div>
              <div class="flex-shrink-0">
                <button
                  @click.stop="viewJob(job.id)"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  View Job
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions and Gamification -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Gamification Panel -->
        <div class="lg:col-span-1">
          <GamificationPanel
            :userData="userData"
            :stats="{
              completedJobs: completedJobsCount,
              averageRating: averageRating
            }"
          />
        </div>

        <!-- Quick Actions -->
        <div class="lg:col-span-2 space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Your Rank</h3>
              <div class="text-center">
                <p class="text-3xl font-bold text-red-600">#{{ userRank || 'N/A' }}</p>
                <p class="text-sm text-gray-500">Global Leaderboard</p>
                <router-link
                  to="/leaderboards"
                  class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-600 bg-red-100 hover:bg-red-200"
                >
                  View Leaderboards
                </router-link>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Profile Completion</h3>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Basic Info</span>
                  <span class="text-sm font-medium text-green-600">✓</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Skills</span>
                  <span class="text-sm font-medium text-green-600">✓</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Portfolio</span>
                  <span class="text-sm font-medium text-yellow-600">Pending</span>
                </div>
                <router-link
                  to="/profile/edit"
                  class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-600 bg-red-100 hover:bg-red-200"
                >
                  Complete Profile
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useJobsStore } from '../../stores/jobs'
import { useLeaderboardStore } from '../../stores/leaderboard'
import GamificationPanel from '../../components/GamificationPanel.vue'

const router = useRouter()
const authStore = useAuthStore()
const jobsStore = useJobsStore()
const leaderboardStore = useLeaderboardStore()

const selectedCategory = ref('')

const userData = computed(() => authStore.userData)
const loading = computed(() => jobsStore.loading)
const availableJobs = computed(() => jobsStore.openJobs)
const userRank = computed(() => leaderboardStore.userRank(authStore.user?.uid))

const activeProposalsCount = computed(() => {
  // This would be calculated from user's proposals
  return 0 // Placeholder
})

const completedJobsCount = computed(() => {
  // This would be calculated from user's job history
  return userData.value?.completedJobs || 0
})

const averageRating = computed(() => {
  // This would be calculated from user's reviews
  return userData.value?.averageRating || 0
})

const filterJobs = async () => {
  await jobsStore.fetchJobs({
    category: selectedCategory.value,
    status: 'open'
  })
}

const viewJob = (jobId) => {
  router.push(`/freelancer/job/${jobId}`)
}

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  return date.toLocaleDateString()
}

onMounted(async () => {
  await Promise.all([
    jobsStore.fetchJobs({ status: 'open' }),
    leaderboardStore.fetchGlobalLeaderboard()
  ])
})
</script>
