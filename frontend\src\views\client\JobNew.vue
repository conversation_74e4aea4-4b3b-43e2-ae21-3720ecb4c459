<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4 max-w-3xl">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Post a New Job</h1>
        <p class="mt-2 text-gray-600">Find the perfect freelancer for your project</p>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-8">
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
          
          <div class="space-y-6">
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700">Job Title</label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                placeholder="e.g., Build a responsive website"
              />
            </div>

            <div>
              <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
              <select
                id="category"
                v-model="form.category"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
              >
                <option value="">Select a category</option>
                <option v-for="category in jobsStore.jobCategories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>

            <div>
              <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="6"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                placeholder="Describe your project in detail..."
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Project Details -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Project Details</h2>
          
          <div class="space-y-6">
            <div>
              <label for="budget" class="block text-sm font-medium text-gray-700">Budget ($)</label>
              <input
                id="budget"
                v-model="form.budget"
                type="number"
                min="5"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                placeholder="100"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Project Duration</label>
              <div class="mt-2 space-y-2">
                <div class="flex items-center">
                  <input
                    id="short-term"
                    v-model="form.duration"
                    name="duration"
                    type="radio"
                    value="short-term"
                    class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
                  />
                  <label for="short-term" class="ml-3 block text-sm font-medium text-gray-700">
                    Short-term (1-7 days) - Available now
                  </label>
                </div>
                <div class="flex items-center opacity-50">
                  <input
                    id="mid-term"
                    name="duration"
                    type="radio"
                    value="mid-term"
                    disabled
                    class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
                  />
                  <label for="mid-term" class="ml-3 block text-sm font-medium text-gray-700">
                    Mid-term (1-4 weeks) - Coming soon
                  </label>
                </div>
                <div class="flex items-center opacity-50">
                  <input
                    id="long-term"
                    name="duration"
                    type="radio"
                    value="long-term"
                    disabled
                    class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
                  />
                  <label for="long-term" class="ml-3 block text-sm font-medium text-gray-700">
                    Long-term (1+ months) - Coming soon
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label for="skills" class="block text-sm font-medium text-gray-700">Required Skills</label>
              <input
                id="skills"
                v-model="form.skills"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                placeholder="JavaScript, React, Node.js (comma separated)"
              />
            </div>

            <div>
              <label for="deadline" class="block text-sm font-medium text-gray-700">Deadline</label>
              <input
                id="deadline"
                v-model="form.deadline"
                type="date"
                :min="minDate"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
              />
            </div>
          </div>
        </div>

        <!-- Additional Requirements -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Additional Requirements</h2>
          
          <div class="space-y-4">
            <div class="flex items-center">
              <input
                id="portfolio-required"
                v-model="form.portfolioRequired"
                type="checkbox"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              <label for="portfolio-required" class="ml-2 block text-sm text-gray-900">
                Portfolio/samples required
              </label>
            </div>
            
            <div class="flex items-center">
              <input
                id="interview-required"
                v-model="form.interviewRequired"
                type="checkbox"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              <label for="interview-required" class="ml-2 block text-sm text-gray-900">
                Interview required before hiring
              </label>
            </div>
          </div>
        </div>

        <!-- Submit -->
        <div class="flex justify-end space-x-4">
          <router-link
            to="/client/dashboard"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? 'Posting...' : 'Post Job' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useJobsStore } from '../../stores/jobs'
import { useAppStore } from '../../stores/app'
import { securityService } from '../../services/security'

const router = useRouter()
const authStore = useAuthStore()
const jobsStore = useJobsStore()
const appStore = useAppStore()

const form = ref({
  title: '',
  category: '',
  description: '',
  budget: '',
  duration: 'short-term',
  skills: '',
  deadline: '',
  portfolioRequired: false,
  interviewRequired: false
})

const loading = computed(() => jobsStore.loading)

const minDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

const handleSubmit = async () => {
  // Check rate limiting
  const rateLimitCheck = securityService.checkRateLimit('jobPost', authStore.user.uid)
  if (!rateLimitCheck.allowed) {
    const resetTime = rateLimitCheck.resetTime
    const resetString = resetTime ? ` Try again at ${resetTime.toLocaleTimeString()}` : ''
    appStore.showError(`Too many job posts.${resetString}`)
    return
  }

  // Comprehensive validation and sanitization
  const validation = securityService.validateJobPosting(form.value)

  if (!validation.isValid) {
    const firstError = Object.values(validation.errors)[0]
    appStore.showError(firstError)
    return
  }

  // Content moderation
  const titleModeration = securityService.moderateContent(validation.sanitized.title, 'job')
  const descModeration = securityService.moderateContent(validation.sanitized.description, 'job')

  if (!titleModeration.approved || !descModeration.approved) {
    appStore.showError('Job content flagged for review. Please revise and try again.')
    return
  }

  const jobData = {
    ...validation.sanitized,
    postedBy: authStore.user.uid,
    clientName: authStore.userData.name
  }

  const result = await jobsStore.postJob(jobData)

  if (result.success) {
    appStore.showSuccess('Job posted successfully!')
    router.push('/client/dashboard')
  }
}

onMounted(() => {
  if (!authStore.isClient) {
    router.push('/')
  }
})
</script>
