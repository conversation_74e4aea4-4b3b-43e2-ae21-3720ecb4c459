import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useAuthStore } from '../stores/auth'
import { useJobsStore } from '../stores/jobs'
import { useGamificationStore } from '../stores/gamification'

// Mock Firebase
vi.mock('../firebase/auth', () => ({
  signUp: vi.fn(),
  signIn: vi.fn(),
  logOut: vi.fn(),
  getUserData: vi.fn(),
  onAuthStateChange: vi.fn()
}))

vi.mock('../firebase/firestore', () => ({
  createJob: vi.fn(),
  getJob: vi.fn(),
  getJobs: vi.fn(),
  submitProposal: vi.fn(),
  updateUserXP: vi.fn()
}))

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with correct default state', () => {
    const authStore = useAuthStore()
    
    expect(authStore.user).toBeNull()
    expect(authStore.userData).toBeNull()
    expect(authStore.loading).toBe(true)
    expect(authStore.error).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
  })

  it('should update user data', () => {
    const authStore = useAuthStore()
    
    const newData = { name: 'John Doe', role: 'freelancer' }
    authStore.updateUserData(newData)
    
    expect(authStore.userData).toEqual(newData)
  })

  it('should clear error', () => {
    const authStore = useAuthStore()
    
    authStore.error = 'Some error'
    authStore.clearError()
    
    expect(authStore.error).toBeNull()
  })

  it('should compute user role correctly', () => {
    const authStore = useAuthStore()
    
    authStore.userData = { role: 'client' }
    expect(authStore.isClient).toBe(true)
    expect(authStore.isFreelancer).toBe(false)
    
    authStore.userData = { role: 'freelancer' }
    expect(authStore.isClient).toBe(false)
    expect(authStore.isFreelancer).toBe(true)
  })
})

describe('Jobs Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with correct default state', () => {
    const jobsStore = useJobsStore()
    
    expect(jobsStore.jobs).toEqual([])
    expect(jobsStore.currentJob).toBeNull()
    expect(jobsStore.loading).toBe(false)
    expect(jobsStore.error).toBeNull()
  })

  it('should filter open jobs correctly', () => {
    const jobsStore = useJobsStore()
    
    jobsStore.jobs = [
      { id: '1', status: 'open', title: 'Job 1' },
      { id: '2', status: 'closed', title: 'Job 2' },
      { id: '3', status: 'open', title: 'Job 3' }
    ]
    
    expect(jobsStore.openJobs).toHaveLength(2)
    expect(jobsStore.openJobs[0].title).toBe('Job 1')
    expect(jobsStore.openJobs[1].title).toBe('Job 3')
  })

  it('should filter jobs by category', () => {
    const jobsStore = useJobsStore()
    
    jobsStore.jobs = [
      { id: '1', category: 'dev', title: 'Dev Job' },
      { id: '2', category: 'design', title: 'Design Job' },
      { id: '3', category: 'dev', title: 'Another Dev Job' }
    ]
    
    jobsStore.setFilters({ category: 'dev' })
    
    expect(jobsStore.jobsByCategory).toHaveLength(2)
    expect(jobsStore.jobsByCategory[0].category).toBe('dev')
    expect(jobsStore.jobsByCategory[1].category).toBe('dev')
  })

  it('should get unique categories', () => {
    const jobsStore = useJobsStore()
    
    jobsStore.jobs = [
      { id: '1', category: 'dev' },
      { id: '2', category: 'design' },
      { id: '3', category: 'dev' },
      { id: '4', category: 'writing' }
    ]
    
    const categories = jobsStore.categories
    expect(categories).toEqual(['design', 'dev', 'writing']) // Should be sorted
  })

  it('should clear filters', () => {
    const jobsStore = useJobsStore()
    
    jobsStore.setFilters({ category: 'dev', status: 'closed' })
    jobsStore.clearFilters()
    
    expect(jobsStore.filters).toEqual({
      category: '',
      status: 'open',
      limit: 20
    })
  })
})

describe('Gamification Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with correct default state', () => {
    const gamificationStore = useGamificationStore()
    
    expect(gamificationStore.achievements).toEqual([])
    expect(gamificationStore.loading).toBe(false)
    expect(gamificationStore.error).toBeNull()
  })

  it('should get tier info correctly', () => {
    const gamificationStore = useGamificationStore()
    
    const tier1 = gamificationStore.getTierInfo(1)
    expect(tier1.name).toBe('Newcomer')
    expect(tier1.xpRequired).toBe(0)
    
    const tier3 = gamificationStore.getTierInfo(3)
    expect(tier3.name).toBe('Professional')
    expect(tier3.xpRequired).toBe(1500)
  })

  it('should calculate tier progress correctly', () => {
    const gamificationStore = useGamificationStore()
    
    // User with 750 XP at tier 2 (needs 1500 for tier 3)
    const progress = gamificationStore.calculateTierProgress(750, 2)
    
    expect(progress.progress).toBe(25) // 250/1000 = 25%
    expect(progress.xpNeeded).toBe(750) // 1500 - 750 = 750
    expect(progress.nextTier.name).toBe('Professional')
  })

  it('should get user badges correctly', () => {
    const gamificationStore = useGamificationStore()
    
    const userBadgeIds = ['newcomer', 'first_gig', 'invalid_badge']
    const badges = gamificationStore.getUserBadges(userBadgeIds)
    
    expect(badges).toHaveLength(2) // Should filter out invalid badge
    expect(badges[0].id).toBe('newcomer')
    expect(badges[1].id).toBe('first_gig')
  })

  it('should clear achievements', () => {
    const gamificationStore = useGamificationStore()
    
    gamificationStore.achievements = [
      { id: 1, type: 'xp', amount: 100 },
      { id: 2, type: 'badge', badge: { name: 'Test Badge' } }
    ]
    
    gamificationStore.clearAchievements()
    expect(gamificationStore.achievements).toEqual([])
  })

  it('should remove specific achievement', () => {
    const gamificationStore = useGamificationStore()
    
    gamificationStore.achievements = [
      { id: 1, type: 'xp', amount: 100 },
      { id: 2, type: 'badge', badge: { name: 'Test Badge' } }
    ]
    
    gamificationStore.removeAchievement(1)
    expect(gamificationStore.achievements).toHaveLength(1)
    expect(gamificationStore.achievements[0].id).toBe(2)
  })

  it('should get recent achievements with limit', () => {
    const gamificationStore = useGamificationStore()
    
    const now = new Date()
    gamificationStore.achievements = [
      { id: 1, timestamp: new Date(now.getTime() - 1000) },
      { id: 2, timestamp: new Date(now.getTime() - 2000) },
      { id: 3, timestamp: new Date(now.getTime() - 3000) },
      { id: 4, timestamp: new Date(now.getTime() - 4000) }
    ]
    
    const recent = gamificationStore.getRecentAchievements(2)
    expect(recent).toHaveLength(2)
    expect(recent[0].id).toBe(1) // Most recent first
    expect(recent[1].id).toBe(2)
  })
})
