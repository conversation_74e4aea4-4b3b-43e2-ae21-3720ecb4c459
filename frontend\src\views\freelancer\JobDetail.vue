<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4 max-w-4xl">
      <div v-if="loading" class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
        <p class="mt-2 text-gray-500">Loading job details...</p>
      </div>

      <div v-else-if="!job" class="text-center">
        <h1 class="text-2xl font-bold text-gray-900">Job not found</h1>
        <router-link to="/freelancer/dashboard" class="mt-4 text-red-600 hover:text-red-500">
          Back to Dashboard
        </router-link>
      </div>

      <div v-else class="space-y-8">
        <!-- Header -->
        <div class="flex justify-between items-start">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ job.title }}</h1>
            <p class="mt-2 text-lg text-gray-600">Posted by {{ job.clientName }}</p>
            <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
              <span class="capitalize">{{ job.category }}</span>
              <span>•</span>
              <span>${{ job.budget }}</span>
              <span>•</span>
              <span>{{ formatDate(job.createdAt) }}</span>
            </div>
          </div>
          <span :class="[
            'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
            job.status === 'open' ? 'bg-green-100 text-green-800' :
            job.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          ]">
            {{ job.status }}
          </span>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Job Details -->
          <div class="lg:col-span-2 space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
              <h2 class="text-lg font-medium text-gray-900 mb-4">Job Description</h2>
              <p class="text-gray-700 whitespace-pre-wrap">{{ job.description }}</p>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
              <h2 class="text-lg font-medium text-gray-900 mb-4">Requirements</h2>

              <div class="space-y-4">
                <div>
                  <h3 class="text-sm font-medium text-gray-900">Required Skills</h3>
                  <div class="mt-2 flex flex-wrap gap-2">
                    <span
                      v-for="skill in job.skills"
                      :key="skill"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {{ skill }}
                    </span>
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <h3 class="text-sm font-medium text-gray-900">Deadline</h3>
                    <p class="mt-1 text-sm text-gray-700">{{ formatDate(job.deadline) }}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-medium text-gray-900">Duration</h3>
                    <p class="mt-1 text-sm text-gray-700 capitalize">{{ job.duration }}</p>
                  </div>
                </div>

                <div v-if="job.portfolioRequired || job.interviewRequired" class="space-y-2">
                  <h3 class="text-sm font-medium text-gray-900">Additional Requirements</h3>
                  <ul class="text-sm text-gray-700 space-y-1">
                    <li v-if="job.portfolioRequired" class="flex items-center">
                      <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      Portfolio/samples required
                    </li>
                    <li v-if="job.interviewRequired" class="flex items-center">
                      <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      Interview required
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Proposal Form -->
          <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg p-6 sticky top-8">
              <h2 class="text-lg font-medium text-gray-900 mb-4">Submit Proposal</h2>

              <div v-if="hasApplied" class="text-center py-4">
                <svg class="mx-auto h-12 w-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Proposal Submitted</h3>
                <p class="mt-1 text-sm text-gray-500">You've already applied to this job.</p>
              </div>

              <form v-else @submit.prevent="submitProposal" class="space-y-4">
                <div>
                  <label for="price" class="block text-sm font-medium text-gray-700">Your Price ($)</label>
                  <input
                    id="price"
                    v-model="proposalForm.price"
                    type="number"
                    min="5"
                    required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                    placeholder="100"
                  />
                </div>

                <div>
                  <label for="coverLetter" class="block text-sm font-medium text-gray-700">Cover Letter</label>
                  <textarea
                    id="coverLetter"
                    v-model="proposalForm.coverLetter"
                    rows="6"
                    required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                    placeholder="Explain why you're the best fit for this job..."
                  ></textarea>
                </div>

                <div>
                  <label for="deliveryTime" class="block text-sm font-medium text-gray-700">Delivery Time (days)</label>
                  <select
                    id="deliveryTime"
                    v-model="proposalForm.deliveryTime"
                    required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                  >
                    <option value="1">1 day</option>
                    <option value="2">2 days</option>
                    <option value="3">3 days</option>
                    <option value="5">5 days</option>
                    <option value="7">1 week</option>
                  </select>
                </div>

                <button
                  type="submit"
                  :disabled="submitting"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {{ submitting ? 'Submitting...' : 'Submit Proposal' }}
                </button>
              </form>

              <!-- Job Stats -->
              <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Job Statistics</h3>
                <div class="space-y-2 text-sm text-gray-600">
                  <div class="flex justify-between">
                    <span>Proposals:</span>
                    <span>{{ proposalCount }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>Avg. Bid:</span>
                    <span>${{ averageBid }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>Time Left:</span>
                    <span>{{ timeLeft }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useJobsStore } from '../../stores/jobs'
import { useAppStore } from '../../stores/app'

const route = useRoute()
const authStore = useAuthStore()
const jobsStore = useJobsStore()
const appStore = useAppStore()

const submitting = ref(false)
const proposalForm = ref({
  price: '',
  coverLetter: '',
  deliveryTime: '3'
})

const job = computed(() => jobsStore.currentJob)
const loading = computed(() => jobsStore.loading)
const proposalCount = computed(() => job.value ? Object.keys(job.value.proposals || {}).length : 0)

const hasApplied = computed(() => {
  if (!job.value || !authStore.user) return false
  return job.value.proposals && job.value.proposals[authStore.user.uid]
})

const averageBid = computed(() => {
  if (!job.value || !job.value.proposals) return 0
  const proposals = Object.values(job.value.proposals)
  if (proposals.length === 0) return 0
  const total = proposals.reduce((sum, proposal) => sum + (proposal.price || 0), 0)
  return Math.round(total / proposals.length)
})

const timeLeft = computed(() => {
  if (!job.value || !job.value.deadline) return 'N/A'
  const deadline = job.value.deadline.toDate ? job.value.deadline.toDate() : new Date(job.value.deadline)
  const now = new Date()
  const diff = deadline - now
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
  return days > 0 ? `${days} days` : 'Expired'
})

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  return date.toLocaleDateString()
}

const submitProposal = async () => {
  if (!proposalForm.value.price || !proposalForm.value.coverLetter) {
    appStore.showError('Please fill in all fields')
    return
  }

  if (proposalForm.value.price < 5) {
    appStore.showError('Minimum price is $5')
    return
  }

  submitting.value = true

  const proposalData = {
    ...proposalForm.value,
    price: parseFloat(proposalForm.value.price),
    freelancerUid: authStore.user.uid,
    freelancerName: authStore.userData.name
  }

  const result = await jobsStore.applyToJob(job.value.id, proposalData)

  if (result.success) {
    appStore.showSuccess('Proposal submitted successfully!')
    proposalForm.value = { price: '', coverLetter: '', deliveryTime: '3' }
  }

  submitting.value = false
}

onMounted(async () => {
  await jobsStore.fetchJob(route.params.id)
})
</script>
