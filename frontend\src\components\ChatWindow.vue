<template>
  <div class="flex flex-col h-96 bg-white border rounded-lg shadow">
    <!-- <PERSON><PERSON> -->
    <div class="flex items-center justify-between p-4 border-b">
      <h3 class="text-lg font-medium text-gray-900">
        {{ chatTitle }}
      </h3>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Messages Area -->
    <div 
      ref="messagesContainer"
      class="flex-1 overflow-y-auto p-4 space-y-4"
    >
      <div v-if="loading" class="text-center">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600 mx-auto"></div>
        <p class="text-sm text-gray-500 mt-2">Loading messages...</p>
      </div>

      <div v-else-if="messages.length === 0" class="text-center py-8">
        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
        </svg>
        <p class="text-sm text-gray-500 mt-2">No messages yet</p>
        <p class="text-xs text-gray-400">Start the conversation!</p>
      </div>

      <div v-else>
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'flex',
            message.isOwn ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
              message.isOwn
                ? 'bg-red-600 text-white'
                : 'bg-gray-100 text-gray-900'
            ]"
          >
            <!-- File attachments -->
            <div v-if="message.attachments && message.attachments.length > 0" class="mb-2">
              <div
                v-for="file in message.attachments"
                :key="file.url"
                class="flex items-center space-x-2 p-2 bg-black bg-opacity-10 rounded"
              >
                <span class="text-lg">{{ getFileIcon(file) }}</span>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium truncate">{{ file.name }}</p>
                  <p class="text-xs opacity-75">{{ formatFileSize(file.size) }}</p>
                </div>
                <a
                  :href="file.url"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-xs underline opacity-75 hover:opacity-100"
                >
                  Download
                </a>
              </div>
            </div>

            <!-- Message text -->
            <p class="text-sm">{{ message.message }}</p>
            
            <!-- Message metadata -->
            <div class="flex items-center justify-between mt-1">
              <span class="text-xs opacity-75">
                {{ formatTime(message.timestamp) }}
              </span>
              <span v-if="message.edited" class="text-xs opacity-75">
                (edited)
              </span>
            </div>
          </div>
        </div>

        <!-- Typing indicator -->
        <div v-if="typingUsers.length > 0" class="flex justify-start">
          <div class="bg-gray-100 px-4 py-2 rounded-lg">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message Input -->
    <div class="border-t p-4">
      <div class="flex items-end space-x-2">
        <!-- File upload -->
        <input
          ref="fileInput"
          type="file"
          multiple
          class="hidden"
          @change="handleFileSelect"
        />
        <button
          @click="$refs.fileInput.click()"
          class="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600"
          title="Attach files"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
        </button>

        <!-- Message input -->
        <div class="flex-1">
          <textarea
            v-model="newMessage"
            @keydown.enter.exact.prevent="sendMessage"
            @keydown.shift.enter="() => {}"
            @input="handleTyping"
            placeholder="Type a message... (Enter to send, Shift+Enter for new line)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
            rows="1"
            style="min-height: 40px; max-height: 120px;"
          ></textarea>
        </div>

        <!-- Send button -->
        <button
          @click="sendMessage"
          :disabled="!newMessage.trim() && selectedFiles.length === 0"
          class="flex-shrink-0 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </div>

      <!-- Selected files preview -->
      <div v-if="selectedFiles.length > 0" class="mt-2 space-y-1">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="flex items-center justify-between p-2 bg-gray-50 rounded"
        >
          <div class="flex items-center space-x-2">
            <span class="text-sm">{{ getFileIcon(file) }}</span>
            <span class="text-sm text-gray-700">{{ file.name }}</span>
            <span class="text-xs text-gray-500">({{ formatFileSize(file.size) }})</span>
          </div>
          <button
            @click="removeFile(index)"
            class="text-red-500 hover:text-red-700"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { useChatStore } from '../stores/chat'
import ChatService from '../services/chat'

const props = defineProps({
  jobId: {
    type: String,
    required: true
  },
  clientId: {
    type: String,
    required: true
  },
  freelancerId: {
    type: String,
    required: true
  },
  currentUserId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close'])

const chatStore = useChatStore()
const messagesContainer = ref(null)
const fileInput = ref(null)
const newMessage = ref('')
const selectedFiles = ref([])
const typingTimeout = ref(null)

const loading = computed(() => chatStore.loading)
const messages = computed(() => chatStore.currentMessages)
const typingUsers = computed(() => chatStore.getTypingUsers(chatStore.currentChatRoom?.id))

const chatTitle = computed(() => {
  if (props.currentUserId === props.clientId) {
    return 'Chat with Freelancer'
  } else {
    return 'Chat with Client'
  }
})

const sendMessage = async () => {
  if (!newMessage.value.trim() && selectedFiles.value.length === 0) return

  try {
    let result
    
    if (selectedFiles.value.length > 0) {
      // Send files
      result = await chatStore.sendFileMessage(selectedFiles.value)
      selectedFiles.value = []
    }
    
    if (newMessage.value.trim()) {
      // Send text message
      result = await chatStore.sendMessage(newMessage.value.trim())
    }

    if (result?.success) {
      newMessage.value = ''
      scrollToBottom()
    }
  } catch (error) {
    console.error('Error sending message:', error)
  }
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  selectedFiles.value.push(...files)
  event.target.value = '' // Reset input
}

const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

const handleTyping = () => {
  if (chatStore.currentChatRoom) {
    chatStore.setTyping(chatStore.currentChatRoom.id, props.currentUserId, true)
    
    // Clear previous timeout
    if (typingTimeout.value) {
      clearTimeout(typingTimeout.value)
    }
    
    // Set new timeout to stop typing indicator
    typingTimeout.value = setTimeout(() => {
      chatStore.setTyping(chatStore.currentChatRoom.id, props.currentUserId, false)
    }, 1000)
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const formatTime = (timestamp) => {
  return chatStore.formatMessageTime(timestamp)
}

const formatFileSize = (bytes) => {
  return ChatService.formatFileSize(bytes)
}

const getFileIcon = (file) => {
  return ChatService.getFileIcon(file)
}

// Watch for new messages and scroll to bottom
watch(messages, () => {
  scrollToBottom()
}, { deep: true })

onMounted(async () => {
  await chatStore.initializeChatRoom(props.jobId, props.clientId, props.freelancerId)
  scrollToBottom()
})

onUnmounted(() => {
  if (typingTimeout.value) {
    clearTimeout(typingTimeout.value)
  }
})
</script>
