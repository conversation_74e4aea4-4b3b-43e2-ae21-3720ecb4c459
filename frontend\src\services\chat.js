import { 
  collection, 
  doc, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot,
  serverTimestamp,
  updateDoc,
  getDocs,
  limit
} from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { db, storage } from '../firebase/config'

export class ChatService {
  static async createChatRoom(jobId, clientId, freelancerId) {
    try {
      const chatRoomData = {
        jobId,
        clientId,
        freelancerId,
        createdAt: serverTimestamp(),
        lastMessage: null,
        lastMessageAt: null,
        unreadCount: {
          [clientId]: 0,
          [freelancerId]: 0
        }
      }

      const docRef = await addDoc(collection(db, 'chatRooms'), chatRoomData)
      return { success: true, chatRoomId: docRef.id }
    } catch (error) {
      console.error('Error creating chat room:', error)
      return { success: false, error: error.message }
    }
  }

  static async getChatRoom(jobId, clientId, freelancerId) {
    try {
      const q = query(
        collection(db, 'chatRooms'),
        where('jobId', '==', jobId),
        where('clientId', '==', clientId),
        where('freelancerId', '==', freelancerId)
      )

      const querySnapshot = await getDocs(q)
      
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0]
        return { success: true, chatRoom: { id: doc.id, ...doc.data() } }
      } else {
        // Create new chat room if it doesn't exist
        return await this.createChatRoom(jobId, clientId, freelancerId)
      }
    } catch (error) {
      console.error('Error getting chat room:', error)
      return { success: false, error: error.message }
    }
  }

  static async sendMessage(chatRoomId, senderId, message, type = 'text', attachments = []) {
    try {
      const messageData = {
        chatRoomId,
        senderId,
        message,
        type, // 'text', 'file', 'image'
        attachments,
        createdAt: serverTimestamp(),
        edited: false,
        editedAt: null
      }

      const docRef = await addDoc(collection(db, 'messages'), messageData)

      // Update chat room with last message
      const chatRoomRef = doc(db, 'chatRooms', chatRoomId)
      await updateDoc(chatRoomRef, {
        lastMessage: message,
        lastMessageAt: serverTimestamp()
      })

      return { success: true, messageId: docRef.id }
    } catch (error) {
      console.error('Error sending message:', error)
      return { success: false, error: error.message }
    }
  }

  static async uploadFile(file, chatRoomId) {
    try {
      const fileName = `${Date.now()}_${file.name}`
      const filePath = `chat/${chatRoomId}/${fileName}`
      const fileRef = ref(storage, filePath)

      const snapshot = await uploadBytes(fileRef, file)
      const downloadURL = await getDownloadURL(snapshot.ref)

      return {
        success: true,
        file: {
          name: file.name,
          size: file.size,
          type: file.type,
          url: downloadURL,
          path: filePath
        }
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      return { success: false, error: error.message }
    }
  }

  static async sendFileMessage(chatRoomId, senderId, files) {
    try {
      const attachments = []

      // Upload all files
      for (const file of files) {
        const uploadResult = await this.uploadFile(file, chatRoomId)
        if (uploadResult.success) {
          attachments.push(uploadResult.file)
        }
      }

      if (attachments.length === 0) {
        return { success: false, error: 'No files uploaded successfully' }
      }

      // Send message with attachments
      const message = attachments.length === 1 
        ? `Sent a file: ${attachments[0].name}`
        : `Sent ${attachments.length} files`

      return await this.sendMessage(chatRoomId, senderId, message, 'file', attachments)
    } catch (error) {
      console.error('Error sending file message:', error)
      return { success: false, error: error.message }
    }
  }

  static subscribeToMessages(chatRoomId, callback) {
    const q = query(
      collection(db, 'messages'),
      where('chatRoomId', '==', chatRoomId),
      orderBy('createdAt', 'asc')
    )

    return onSnapshot(q, (querySnapshot) => {
      const messages = []
      querySnapshot.forEach((doc) => {
        messages.push({ id: doc.id, ...doc.data() })
      })
      callback(messages)
    })
  }

  static subscribeToUserChatRooms(userId, callback) {
    const q1 = query(
      collection(db, 'chatRooms'),
      where('clientId', '==', userId),
      orderBy('lastMessageAt', 'desc')
    )

    const q2 = query(
      collection(db, 'chatRooms'),
      where('freelancerId', '==', userId),
      orderBy('lastMessageAt', 'desc')
    )

    // Subscribe to both queries
    const unsubscribe1 = onSnapshot(q1, (snapshot) => {
      const chatRooms = []
      snapshot.forEach((doc) => {
        chatRooms.push({ id: doc.id, ...doc.data() })
      })
      callback(chatRooms, 'client')
    })

    const unsubscribe2 = onSnapshot(q2, (snapshot) => {
      const chatRooms = []
      snapshot.forEach((doc) => {
        chatRooms.push({ id: doc.id, ...doc.data() })
      })
      callback(chatRooms, 'freelancer')
    })

    return () => {
      unsubscribe1()
      unsubscribe2()
    }
  }

  static async markAsRead(chatRoomId, userId) {
    try {
      const chatRoomRef = doc(db, 'chatRooms', chatRoomId)
      await updateDoc(chatRoomRef, {
        [`unreadCount.${userId}`]: 0
      })
      return { success: true }
    } catch (error) {
      console.error('Error marking as read:', error)
      return { success: false, error: error.message }
    }
  }

  static async editMessage(messageId, newMessage) {
    try {
      const messageRef = doc(db, 'messages', messageId)
      await updateDoc(messageRef, {
        message: newMessage,
        edited: true,
        editedAt: serverTimestamp()
      })
      return { success: true }
    } catch (error) {
      console.error('Error editing message:', error)
      return { success: false, error: error.message }
    }
  }

  static async getRecentMessages(chatRoomId, limitCount = 50) {
    try {
      const q = query(
        collection(db, 'messages'),
        where('chatRoomId', '==', chatRoomId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )

      const querySnapshot = await getDocs(q)
      const messages = []
      
      querySnapshot.forEach((doc) => {
        messages.push({ id: doc.id, ...doc.data() })
      })

      return { success: true, messages: messages.reverse() }
    } catch (error) {
      console.error('Error getting recent messages:', error)
      return { success: false, error: error.message }
    }
  }

  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  static isImageFile(file) {
    return file.type && file.type.startsWith('image/')
  }

  static isCodeFile(file) {
    const codeExtensions = ['.js', '.ts', '.jsx', '.tsx', '.vue', '.py', '.java', '.cpp', '.c', '.html', '.css', '.scss', '.json', '.xml']
    return codeExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
  }

  static getFileIcon(file) {
    if (this.isImageFile(file)) return '🖼️'
    if (this.isCodeFile(file)) return '💻'
    if (file.type.includes('pdf')) return '📄'
    if (file.type.includes('word')) return '📝'
    if (file.type.includes('excel') || file.type.includes('spreadsheet')) return '📊'
    if (file.type.includes('zip') || file.type.includes('rar')) return '📦'
    return '📎'
  }
}

export default ChatService
