# Gignify - Gamified Freelancing Platform

Gignify is a modern, gamified freelancing platform that connects clients with talented freelancers while making the experience engaging through XP, badges, tiers, and leaderboards.

## 🚀 Features

### Core Platform Features
- **User Authentication**: Secure signup/login with Firebase Auth
- **Dual User Roles**: Separate experiences for clients and freelancers
- **Job Management**: Post, browse, and apply to jobs
- **Real-time Chat**: Built-in messaging system for project communication
- **Payment Integration**: Stripe-powered secure payments (MVP simulation)

### Gamification System
- **XP System**: Earn experience points for completing jobs and activities
- **Tier Progression**: 5-tier system from Newcomer to Master
- **Badge System**: 10+ achievement badges for various accomplishments
- **Leaderboards**: Global and category-specific rankings
- **Freelancer of the Month**: Featured top performer

### Security & Quality
- **Input Validation**: Comprehensive form validation and sanitization
- **Spam Detection**: AI-powered content moderation
- **Rate Limiting**: Protection against abuse
- **Firestore Security Rules**: Database-level access control

## 🛠 Tech Stack

### Frontend
- **Vue 3** with Composition API
- **Pinia** for state management
- **Vue Router** for navigation
- **TailwindCSS** for styling
- **Vite** for build tooling
- **Vitest** for testing

### Backend & Services
- **Firebase Auth** for authentication
- **Firestore** for database
- **Firebase Storage** for file uploads
- **Stripe** for payment processing
- **Firebase Hosting** for deployment

## 📁 Project Structure

```
gignify/
├── frontend/
│   ├── src/
│   │   ├── components/          # Reusable Vue components
│   │   ├── views/              # Page components
│   │   │   ├── client/         # Client-specific pages
│   │   │   ├── freelancer/     # Freelancer-specific pages
│   │   │   └── admin/          # Admin panel
│   │   ├── stores/             # Pinia stores
│   │   ├── services/           # Business logic services
│   │   ├── utils/              # Utility functions
│   │   ├── firebase/           # Firebase configuration
│   │   └── tests/              # Test files
│   ├── public/                 # Static assets
│   └── package.json
├── firestore.rules             # Firestore security rules
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Firebase project with Firestore and Auth enabled
- Stripe account (for payment features)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gignify
   ```

2. **Install dependencies**
   ```bash
   cd frontend
   npm install
   ```

3. **Environment Setup**
   Create `frontend/.env` file:
   ```env
   VITE_FIREBASE_API_KEY=your_firebase_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
   ```

4. **Firebase Setup**
   - Deploy Firestore security rules:
     ```bash
     firebase deploy --only firestore:rules
     ```
   - Enable Authentication providers (Email/Password)
   - Create Firestore database in production mode

5. **Start Development Server**
   ```bash
   npm run dev
   ```

## 🧪 Testing

Run the test suite:
```bash
# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Run tests once
npm run test:run
```

## 🏗 Build & Deploy

1. **Build for production**
   ```bash
   npm run build
   ```

2. **Deploy to Firebase Hosting**
   ```bash
   firebase deploy --only hosting
   ```

## 📊 Database Schema

### Users Collection
```javascript
{
  uid: string,
  name: string,
  email: string,
  role: 'client' | 'freelancer',
  xp: number,
  tier: number,
  badges: string[],
  createdAt: timestamp,
  // ... additional profile fields
}
```

### Jobs Collection
```javascript
{
  id: string,
  title: string,
  description: string,
  budget: number,
  category: string,
  status: 'open' | 'in-progress' | 'completed',
  postedBy: string,
  proposals: object,
  createdAt: timestamp,
  deadline: timestamp
}
```

### Messages Collection
```javascript
{
  id: string,
  chatRoomId: string,
  senderId: string,
  message: string,
  type: 'text' | 'file',
  attachments: array,
  createdAt: timestamp
}
```

## 🎮 Gamification System

### XP Values
- Complete Job: 100 XP
- 5-Star Review: 50 XP
- Job Streak (5): 100 XP
- Profile Complete: 50 XP

### Tier System
1. **Newcomer** (0 XP) - Basic profile
2. **Rising Star** (500 XP) - Priority support, profile boost
3. **Professional** (1,500 XP) - Featured in search, lower fees
4. **Expert** (3,000 XP) - Expert badge, premium features
5. **Master** (6,000 XP) - Master status, exclusive opportunities

### Badge Categories
- **Achievement Badges**: First Gig, Top Performer, Master Craftsman
- **Skill Badges**: Expert Verified, Speed Demon
- **Social Badges**: Client Favorite, Community Champion

## 🔒 Security Features

### Input Validation
- Email format validation
- Password strength requirements
- Content length limits
- File type restrictions

### Spam Detection
- Suspicious pattern recognition
- Content moderation
- User behavior analysis
- Rate limiting

### Access Control
- Role-based permissions
- Firestore security rules
- Authentication requirements
- Admin-only operations

## 🚧 MVP Limitations

This is an MVP with some simulated features:
- **Payments**: Stripe integration redirects to "coming soon" page
- **File Uploads**: Basic implementation without cloud storage
- **Video Chat**: Placeholder for future implementation
- **Advanced Analytics**: Basic stats only

## 🔮 Future Enhancements

### Phase 2 Features
- Real Stripe payment processing
- Milestone-based payments
- Advanced project management
- Video chat integration
- Mobile app (React Native)

### Phase 3 Features
- AI-powered job matching
- Skill verification tests
- Team collaboration tools
- Advanced analytics dashboard
- Multi-language support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Vue 3 Composition API patterns
- Use TypeScript for new features
- Write tests for new functionality
- Follow the existing code style
- Update documentation for new features

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Vue.js team for the amazing framework
- Firebase team for the backend services
- TailwindCSS for the utility-first CSS framework
- Stripe for payment processing capabilities
- The open-source community for inspiration and tools

---

**Built with ❤️ by the Gignify Team**