<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON><PERSON> -->
    <div class="bg-white shadow">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">Welcome, Admin</span>
            <button
              @click="logout"
              class="text-red-600 hover:text-red-500 text-sm"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4 py-8">
      <!-- Stats Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Total Users</h3>
              <p class="text-2xl font-bold text-blue-600">{{ stats.totalUsers }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Active Jobs</h3>
              <p class="text-2xl font-bold text-green-600">{{ stats.activeJobs }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Revenue</h3>
              <p class="text-2xl font-bold text-yellow-600">${{ stats.totalRevenue }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Pending Reports</h3>
              <p class="text-2xl font-bold text-red-600">{{ stats.pendingReports }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Tabs -->
      <div class="mb-8">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === tab.id
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="bg-white shadow rounded-lg">
        <!-- Users Management -->
        <div v-if="activeTab === 'users'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-medium text-gray-900">User Management</h2>
            <div class="flex space-x-2">
              <select
                v-model="userFilter"
                class="border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
              >
                <option value="">All Users</option>
                <option value="client">Clients</option>
                <option value="freelancer">Freelancers</option>
              </select>
              <button
                @click="exportUsers"
                class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Export CSV
              </button>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="user in filteredUsers" :key="user.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                        <span class="text-white font-medium">{{ user.name.charAt(0) }}</span>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="capitalize text-sm text-gray-900">{{ user.role }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    ]">
                      {{ user.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(user.createdAt) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      @click="toggleUserStatus(user)"
                      :class="[
                        'mr-2 px-3 py-1 rounded text-xs',
                        user.status === 'active' 
                          ? 'bg-red-100 text-red-800 hover:bg-red-200' 
                          : 'bg-green-100 text-green-800 hover:bg-green-200'
                      ]"
                    >
                      {{ user.status === 'active' ? 'Suspend' : 'Activate' }}
                    </button>
                    <button
                      @click="viewUserDetails(user)"
                      class="text-red-600 hover:text-red-900"
                    >
                      View
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Jobs Management -->
        <div v-if="activeTab === 'jobs'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-medium text-gray-900">Job Management</h2>
            <select
              v-model="jobFilter"
              class="border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
            >
              <option value="">All Jobs</option>
              <option value="open">Open</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="flagged">Flagged</option>
            </select>
          </div>

          <div class="space-y-4">
            <div
              v-for="job in filteredJobs"
              :key="job.id"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h3 class="text-lg font-medium text-gray-900">{{ job.title }}</h3>
                  <p class="text-sm text-gray-500 mt-1">{{ job.description.substring(0, 150) }}...</p>
                  <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>Budget: ${{ job.budget }}</span>
                    <span>Category: {{ job.category }}</span>
                    <span>Posted: {{ formatDate(job.createdAt) }}</span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    job.status === 'open' ? 'bg-green-100 text-green-800' :
                    job.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                    job.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ job.status }}
                  </span>
                  <button
                    @click="moderateJob(job)"
                    class="text-red-600 hover:text-red-900 text-sm"
                  >
                    Moderate
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Reports -->
        <div v-if="activeTab === 'reports'" class="p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Reports & Moderation</h2>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No reports yet</h3>
            <p class="mt-1 text-sm text-gray-500">Reports and moderation tools will appear here.</p>
          </div>
        </div>

        <!-- Analytics -->
        <div v-if="activeTab === 'analytics'" class="p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Platform Analytics</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-50 p-4 rounded-lg">
              <h3 class="text-sm font-medium text-gray-900 mb-2">User Growth</h3>
              <div class="text-2xl font-bold text-blue-600">+{{ stats.userGrowth }}%</div>
              <p class="text-sm text-gray-500">This month</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <h3 class="text-sm font-medium text-gray-900 mb-2">Job Completion Rate</h3>
              <div class="text-2xl font-bold text-green-600">{{ stats.completionRate }}%</div>
              <p class="text-sm text-gray-500">Last 30 days</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const activeTab = ref('users')
const userFilter = ref('')
const jobFilter = ref('')

const tabs = [
  { id: 'users', name: 'Users' },
  { id: 'jobs', name: 'Jobs' },
  { id: 'reports', name: 'Reports' },
  { id: 'analytics', name: 'Analytics' }
]

// Mock admin stats
const stats = ref({
  totalUsers: 1247,
  activeJobs: 89,
  totalRevenue: 48500,
  pendingReports: 3,
  userGrowth: 12,
  completionRate: 94
})

// Mock users data
const users = ref([
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'freelancer',
    status: 'active',
    createdAt: new Date('2024-01-15')
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'client',
    status: 'active',
    createdAt: new Date('2024-02-01')
  },
  {
    id: '3',
    name: 'Bob Wilson',
    email: '<EMAIL>',
    role: 'freelancer',
    status: 'suspended',
    createdAt: new Date('2024-01-20')
  }
])

// Mock jobs data
const jobs = ref([
  {
    id: '1',
    title: 'Build a React Website',
    description: 'Need a modern, responsive website built with React and TailwindCSS. Should include user authentication and payment integration.',
    budget: 1500,
    category: 'dev',
    status: 'open',
    createdAt: new Date('2024-02-10')
  },
  {
    id: '2',
    title: 'Logo Design for Startup',
    description: 'Looking for a creative logo design for a new tech startup. Modern, clean design preferred.',
    budget: 300,
    category: 'design',
    status: 'in-progress',
    createdAt: new Date('2024-02-08')
  },
  {
    id: '3',
    title: 'Content Writing for Blog',
    description: 'Need 10 blog posts written about technology trends. Each post should be 1000+ words.',
    budget: 500,
    category: 'writing',
    status: 'completed',
    createdAt: new Date('2024-02-05')
  }
])

// Computed properties
const filteredUsers = computed(() => {
  if (!userFilter.value) return users.value
  return users.value.filter(user => user.role === userFilter.value)
})

const filteredJobs = computed(() => {
  if (!jobFilter.value) return jobs.value
  return jobs.value.filter(job => job.status === jobFilter.value)
})

// Methods
const formatDate = (date) => {
  return date.toLocaleDateString()
}

const toggleUserStatus = (user) => {
  user.status = user.status === 'active' ? 'suspended' : 'active'
  // In real app, this would make an API call
  console.log(`User ${user.name} status changed to ${user.status}`)
}

const viewUserDetails = (user) => {
  // In real app, this would open a detailed user view
  console.log('Viewing user details:', user)
}

const moderateJob = (job) => {
  // In real app, this would open job moderation tools
  console.log('Moderating job:', job)
}

const exportUsers = () => {
  // In real app, this would generate and download a CSV file
  console.log('Exporting users to CSV')

  // Mock CSV generation
  const csvContent = [
    'Name,Email,Role,Status,Joined',
    ...filteredUsers.value.map(user =>
      `${user.name},${user.email},${user.role},${user.status},${formatDate(user.createdAt)}`
    )
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'gignify_users.csv'
  a.click()
  window.URL.revokeObjectURL(url)
}

const logout = () => {
  // In real app, this would clear admin session
  router.push('/')
}

onMounted(() => {
  // In real app, this would check admin authentication
  console.log('Admin dashboard loaded')
})
</script>

<style scoped>
/* Admin-specific styles */
</style>
