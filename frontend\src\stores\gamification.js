import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import GamificationService, { BADGES, TIERS, XP_VALUES } from '../services/gamification'

export const useGamificationStore = defineStore('gamification', () => {
  // State
  const achievements = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const availableBadges = computed(() => Object.values(BADGES))
  const availableTiers = computed(() => TIERS)
  const xpValues = computed(() => XP_VALUES)

  // Actions
  const awardXP = async (userId, action, metadata = {}) => {
    loading.value = true
    error.value = null

    try {
      const result = await GamificationService.awardXP(userId, action, metadata)
      
      if (result.success) {
        // Add achievements to the list for notifications
        achievements.value.push({
          id: Date.now(),
          type: 'xp',
          amount: result.xpAwarded,
          timestamp: new Date()
        })

        // Add badge achievements
        if (result.newBadges && result.newBadges.length > 0) {
          result.newBadges.forEach(badge => {
            achievements.value.push({
              id: Date.now() + Math.random(),
              type: 'badge',
              badge,
              timestamp: new Date()
            })
          })
        }

        // Add tier promotion achievement
        if (result.tierPromotion && result.tierPromotion.promoted) {
          achievements.value.push({
            id: Date.now() + Math.random() * 2,
            type: 'tier',
            tierInfo: result.tierPromotion.tierInfo,
            timestamp: new Date()
          })
        }

        return result
      } else {
        error.value = result.error
        return result
      }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const calculateTierProgress = (currentXP, currentTier) => {
    return GamificationService.calculateProgress(currentXP, currentTier)
  }

  const getTierInfo = (tier) => {
    return GamificationService.getTierInfo(tier)
  }

  const getBadgeInfo = (badgeId) => {
    return GamificationService.getBadgeInfo(badgeId)
  }

  const getUserBadges = (userBadgeIds) => {
    if (!userBadgeIds || !Array.isArray(userBadgeIds)) return []
    return userBadgeIds.map(badgeId => getBadgeInfo(badgeId)).filter(Boolean)
  }

  const getRecentAchievements = (limit = 5) => {
    return achievements.value
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)
  }

  const clearAchievements = () => {
    achievements.value = []
  }

  const removeAchievement = (achievementId) => {
    const index = achievements.value.findIndex(a => a.id === achievementId)
    if (index > -1) {
      achievements.value.splice(index, 1)
    }
  }

  // Job completion with gamification
  const completeJob = async (userId, jobData) => {
    const metadata = {
      isFirstJob: jobData.isFirstJob || false,
      streak: jobData.streak || 0,
      completedAheadOfDeadline: jobData.completedAheadOfDeadline || false,
      rating: jobData.rating || 0,
      totalJobs: jobData.totalJobs || 0,
      averageRating: jobData.averageRating || 0
    }

    const result = await awardXP(userId, 'COMPLETE_JOB', metadata)
    
    // Award additional XP based on rating
    if (jobData.rating >= 5) {
      await awardXP(userId, 'REVIEW_5_STAR', metadata)
    } else if (jobData.rating >= 4) {
      await awardXP(userId, 'REVIEW_4_STAR', metadata)
    } else if (jobData.rating >= 3) {
      await awardXP(userId, 'REVIEW_3_STAR', metadata)
    }

    return result
  }

  // Streak tracking
  const updateStreak = async (userId, streakCount) => {
    if (streakCount === 5) {
      return await awardXP(userId, 'STREAK_5_JOBS', { streak: streakCount })
    } else if (streakCount === 10) {
      return await awardXP(userId, 'STREAK_10_JOBS', { streak: streakCount })
    } else if (streakCount === 20) {
      return await awardXP(userId, 'STREAK_20_JOBS', { streak: streakCount })
    }
    return { success: true }
  }

  // Client favorite
  const markClientFavorite = async (userId, favoriteCount) => {
    return await awardXP(userId, 'CLIENT_FAVORITE', { favoriteCount })
  }

  // Profile completion rewards
  const rewardProfileCompletion = async (userId) => {
    return await awardXP(userId, 'PROFILE_COMPLETE')
  }

  const rewardPortfolioUpload = async (userId) => {
    return await awardXP(userId, 'PORTFOLIO_UPLOAD')
  }

  const rewardSkillVerification = async (userId) => {
    return await awardXP(userId, 'SKILL_VERIFICATION')
  }

  // Leaderboard helpers
  const getLeaderboardBonus = (position) => {
    if (position === 1) return 500
    if (position <= 3) return 250
    if (position <= 10) return 100
    if (position <= 50) return 50
    return 0
  }

  // Achievement formatting for notifications
  const formatAchievementNotification = (achievement) => {
    return GamificationService.formatAchievementNotification(achievement)
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    achievements,
    loading,
    error,
    
    // Getters
    availableBadges,
    availableTiers,
    xpValues,
    
    // Actions
    awardXP,
    calculateTierProgress,
    getTierInfo,
    getBadgeInfo,
    getUserBadges,
    getRecentAchievements,
    clearAchievements,
    removeAchievement,
    completeJob,
    updateStreak,
    markClientFavorite,
    rewardProfileCompletion,
    rewardPortfolioUpload,
    rewardSkillVerification,
    getLeaderboardBonus,
    formatAchievementNotification,
    clearError
  }
})
