rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    function isValidUser() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isClient() {
      return isAuthenticated() && getUserRole() == 'client';
    }
    
    function isFreelancer() {
      return isAuthenticated() && getUserRole() == 'freelancer';
    }

    // Users collection
    match /users/{userId} {
      // Users can read their own profile and other users' public info
      allow read: if isAuthenticated() && 
                     (isOwner(userId) || 
                      request.auth.uid != userId); // Can read others' public profiles
      
      // Users can only create/update their own profile
      allow create: if isAuthenticated() && 
                       isOwner(userId) &&
                       isValidUserData();
      
      allow update: if isAuthenticated() && 
                       isOwner(userId) &&
                       isValidUserUpdate();
      
      // Only admins can delete users
      allow delete: if isAdmin();
      
      function isValidUserData() {
        let data = request.resource.data;
        return data.keys().hasAll(['name', 'email', 'role', 'createdAt']) &&
               data.name is string &&
               data.name.size() >= 2 &&
               data.name.size() <= 50 &&
               data.email is string &&
               data.email.matches('.*@.*\\..*') &&
               data.role in ['client', 'freelancer'] &&
               data.createdAt is timestamp &&
               data.uid == userId;
      }
      
      function isValidUserUpdate() {
        let data = request.resource.data;
        let existingData = resource.data;
        
        // Cannot change core fields
        return data.uid == existingData.uid &&
               data.email == existingData.email &&
               data.role == existingData.role &&
               data.createdAt == existingData.createdAt;
      }
    }

    // Jobs collection
    match /jobs/{jobId} {
      // Anyone can read open jobs
      allow read: if isAuthenticated();
      
      // Only clients can create jobs
      allow create: if isClient() && isValidJobData();
      
      // Only job owner can update their jobs
      allow update: if isAuthenticated() && 
                       (isOwner(resource.data.postedBy) || isAdmin()) &&
                       isValidJobUpdate();
      
      // Only job owner or admin can delete jobs
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.postedBy) || isAdmin());
      
      function isValidJobData() {
        let data = request.resource.data;
        return data.keys().hasAll(['title', 'description', 'budget', 'category', 'postedBy', 'createdAt']) &&
               data.title is string &&
               data.title.size() >= 5 &&
               data.title.size() <= 100 &&
               data.description is string &&
               data.description.size() >= 20 &&
               data.description.size() <= 5000 &&
               data.budget is number &&
               data.budget >= 5 &&
               data.budget <= 100000 &&
               data.category is string &&
               data.category in ['ai', 'dev', 'design', 'writing', 'marketing', 'video', 'music', 'business'] &&
               data.postedBy == request.auth.uid &&
               data.createdAt is timestamp;
      }
      
      function isValidJobUpdate() {
        let data = request.resource.data;
        let existingData = resource.data;
        
        // Cannot change core fields
        return data.postedBy == existingData.postedBy &&
               data.createdAt == existingData.createdAt;
      }
    }

    // Messages collection (for chat)
    match /messages/{messageId} {
      // Users can read messages in chat rooms they're part of
      allow read: if isAuthenticated() && isParticipantInChat();
      
      // Users can create messages in chat rooms they're part of
      allow create: if isAuthenticated() && 
                       isParticipantInChat() &&
                       isValidMessageData();
      
      // Users can update their own messages (for editing)
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.senderId) &&
                       isValidMessageUpdate();
      
      // Only message sender or admin can delete messages
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.senderId) || isAdmin());
      
      function isParticipantInChat() {
        let chatRoom = get(/databases/$(database)/documents/chatRooms/$(request.resource.data.chatRoomId)).data;
        return request.auth.uid == chatRoom.clientId || 
               request.auth.uid == chatRoom.freelancerId;
      }
      
      function isValidMessageData() {
        let data = request.resource.data;
        return data.keys().hasAll(['chatRoomId', 'senderId', 'message', 'createdAt']) &&
               data.senderId == request.auth.uid &&
               data.message is string &&
               data.message.size() <= 2000 &&
               data.createdAt is timestamp;
      }
      
      function isValidMessageUpdate() {
        let data = request.resource.data;
        let existingData = resource.data;
        
        // Can only update message content and edited status
        return data.chatRoomId == existingData.chatRoomId &&
               data.senderId == existingData.senderId &&
               data.createdAt == existingData.createdAt;
      }
    }

    // Chat rooms collection
    match /chatRooms/{chatRoomId} {
      // Participants can read their chat rooms
      allow read: if isAuthenticated() && isParticipant();
      
      // System can create chat rooms (handled by cloud functions)
      allow create: if isAuthenticated() && isValidChatRoomData();
      
      // Participants can update chat room metadata
      allow update: if isAuthenticated() && isParticipant();
      
      // Only admin can delete chat rooms
      allow delete: if isAdmin();
      
      function isParticipant() {
        return request.auth.uid == resource.data.clientId || 
               request.auth.uid == resource.data.freelancerId;
      }
      
      function isValidChatRoomData() {
        let data = request.resource.data;
        return data.keys().hasAll(['jobId', 'clientId', 'freelancerId', 'createdAt']) &&
               (data.clientId == request.auth.uid || data.freelancerId == request.auth.uid);
      }
    }

    // XP Logs collection
    match /xpLogs/{logId} {
      // Users can read their own XP logs
      allow read: if isAuthenticated() && isOwner(resource.data.uid);
      
      // Only system/admin can create XP logs
      allow create: if isAdmin();
      
      // No updates or deletes allowed
      allow update, delete: if false;
    }

    // Leaderboards collection (read-only for users)
    match /leaderboards/{document=**} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Reviews collection
    match /reviews/{reviewId} {
      // Anyone can read reviews
      allow read: if isAuthenticated();
      
      // Only clients can create reviews for completed jobs
      allow create: if isClient() && isValidReviewData();
      
      // Only review author can update their review
      allow update: if isAuthenticated() && isOwner(resource.data.clientUid);
      
      // Only review author or admin can delete reviews
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.clientUid) || isAdmin());
      
      function isValidReviewData() {
        let data = request.resource.data;
        return data.keys().hasAll(['jobId', 'clientUid', 'freelancerUid', 'rating', 'createdAt']) &&
               data.clientUid == request.auth.uid &&
               data.rating is number &&
               data.rating >= 1 &&
               data.rating <= 5 &&
               data.createdAt is timestamp;
      }
    }

    // Admin collection (for admin user management)
    match /admins/{adminId} {
      allow read, write: if isAdmin();
    }

    // Default deny all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
