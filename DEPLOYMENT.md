# Gignify Deployment Guide

This guide covers deploying Gignify to production using Firebase Hosting and configuring all necessary services.

## 🚀 Production Deployment

### Prerequisites

1. **Firebase CLI** installed globally
   ```bash
   npm install -g firebase-tools
   ```

2. **Firebase Project** created at [Firebase Console](https://console.firebase.google.com)

3. **Stripe Account** for payment processing

### Step 1: Firebase Project Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com)
   - Click "Create a project"
   - Follow the setup wizard

2. **Enable Required Services**
   ```bash
   # Authentication
   - Go to Authentication > Sign-in method
   - Enable Email/Password provider
   
   # Firestore Database
   - Go to Firestore Database
   - Create database in production mode
   - Choose your region
   
   # Storage
   - Go to Storage
   - Get started with default rules
   
   # Hosting
   - Go to Hosting
   - Get started
   ```

### Step 2: Environment Configuration

1. **Get Firebase Config**
   - Go to Project Settings > General
   - Scroll to "Your apps" section
   - Add web app if not exists
   - Copy the config object

2. **Create Production Environment File**
   ```bash
   # Create frontend/.env.production
   VITE_FIREBASE_API_KEY=your_production_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_stripe_key
   ```

### Step 3: Firestore Security Rules

1. **Deploy Security Rules**
   ```bash
   # From project root
   firebase login
   firebase init firestore
   # Select existing project
   # Use firestore.rules as rules file
   # Use firestore.indexes.json as indexes file
   
   firebase deploy --only firestore:rules
   ```

2. **Verify Rules Deployment**
   - Go to Firestore > Rules in Firebase Console
   - Ensure rules are updated

### Step 4: Build and Deploy

1. **Install Dependencies**
   ```bash
   cd frontend
   npm ci --production
   ```

2. **Build for Production**
   ```bash
   npm run build
   ```

3. **Initialize Firebase Hosting**
   ```bash
   firebase init hosting
   # Select existing project
   # Set public directory to: dist
   # Configure as single-page app: Yes
   # Set up automatic builds: No (for now)
   ```

4. **Deploy to Hosting**
   ```bash
   firebase deploy --only hosting
   ```

### Step 5: Stripe Configuration

1. **Production Webhook Setup**
   ```bash
   # In Stripe Dashboard > Developers > Webhooks
   # Add endpoint: https://your-domain.web.app/api/webhooks/stripe
   # Select events:
   - payment_intent.succeeded
   - payment_intent.payment_failed
   - account.updated
   ```

2. **Environment Variables**
   - Update `.env.production` with live Stripe keys
   - Never commit live keys to version control

### Step 6: Domain Configuration (Optional)

1. **Custom Domain Setup**
   ```bash
   # In Firebase Console > Hosting
   # Click "Add custom domain"
   # Follow DNS configuration steps
   ```

2. **SSL Certificate**
   - Firebase automatically provisions SSL certificates
   - May take up to 24 hours to activate

## 🔧 CI/CD Pipeline

### GitHub Actions Setup

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Firebase Hosting

on:
  push:
    branches: [ main ]

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
      
      - name: Build
        run: |
          cd frontend
          npm run build
        env:
          VITE_FIREBASE_API_KEY: ${{ secrets.VITE_FIREBASE_API_KEY }}
          VITE_FIREBASE_AUTH_DOMAIN: ${{ secrets.VITE_FIREBASE_AUTH_DOMAIN }}
          VITE_FIREBASE_PROJECT_ID: ${{ secrets.VITE_FIREBASE_PROJECT_ID }}
          VITE_FIREBASE_STORAGE_BUCKET: ${{ secrets.VITE_FIREBASE_STORAGE_BUCKET }}
          VITE_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.VITE_FIREBASE_MESSAGING_SENDER_ID }}
          VITE_FIREBASE_APP_ID: ${{ secrets.VITE_FIREBASE_APP_ID }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.VITE_STRIPE_PUBLISHABLE_KEY }}
      
      - name: Deploy to Firebase
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          channelId: live
          projectId: your-project-id
```

### Required GitHub Secrets

Add these secrets in GitHub repository settings:

```
VITE_FIREBASE_API_KEY
VITE_FIREBASE_AUTH_DOMAIN
VITE_FIREBASE_PROJECT_ID
VITE_FIREBASE_STORAGE_BUCKET
VITE_FIREBASE_MESSAGING_SENDER_ID
VITE_FIREBASE_APP_ID
VITE_STRIPE_PUBLISHABLE_KEY
FIREBASE_SERVICE_ACCOUNT (JSON key from Firebase)
```

## 📊 Monitoring and Analytics

### Firebase Analytics

1. **Enable Analytics**
   ```bash
   # In Firebase Console > Analytics
   # Enable Google Analytics
   # Create or link Analytics account
   ```

2. **Performance Monitoring**
   ```bash
   # In Firebase Console > Performance
   # Enable Performance Monitoring
   ```

### Error Tracking

1. **Crashlytics Setup**
   ```bash
   # Add to frontend/src/main.js
   import { getAnalytics } from 'firebase/analytics'
   import { getPerformance } from 'firebase/performance'
   
   if (import.meta.env.PROD) {
     getAnalytics(app)
     getPerformance(app)
   }
   ```

## 🔒 Security Checklist

### Pre-Deployment Security

- [ ] Firestore security rules deployed and tested
- [ ] Environment variables properly configured
- [ ] No sensitive data in client-side code
- [ ] CORS properly configured
- [ ] Rate limiting implemented
- [ ] Input validation on all forms
- [ ] XSS protection enabled

### Post-Deployment Security

- [ ] SSL certificate active
- [ ] Security headers configured
- [ ] Regular security audits scheduled
- [ ] Backup strategy implemented
- [ ] Monitoring alerts configured

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

2. **Firestore Permission Denied**
   ```bash
   # Check security rules
   firebase firestore:rules:get
   # Test rules in Firebase Console
   ```

3. **Environment Variables Not Loading**
   ```bash
   # Verify .env file location and naming
   # Check Vite environment variable prefix (VITE_)
   ```

4. **Stripe Webhook Issues**
   ```bash
   # Verify webhook URL is accessible
   # Check webhook secret configuration
   # Review Stripe dashboard logs
   ```

### Performance Optimization

1. **Bundle Analysis**
   ```bash
   npm run build -- --analyze
   ```

2. **Lighthouse Audit**
   ```bash
   # Run in Chrome DevTools
   # Focus on Performance, Accessibility, SEO
   ```

3. **Firebase Performance**
   ```bash
   # Monitor in Firebase Console > Performance
   # Set up custom traces for critical user journeys
   ```

## 📈 Scaling Considerations

### Database Optimization

- Index frequently queried fields
- Implement pagination for large datasets
- Use subcollections for hierarchical data
- Monitor read/write usage

### Hosting Optimization

- Enable compression
- Implement caching strategies
- Use CDN for static assets
- Optimize images and fonts

### Cost Management

- Monitor Firebase usage in console
- Set up billing alerts
- Optimize Firestore queries
- Implement efficient data structures

---

**Need help?** Check the [Firebase Documentation](https://firebase.google.com/docs) or create an issue in the repository.
