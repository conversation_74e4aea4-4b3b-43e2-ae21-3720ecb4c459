import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import ChatService from '../services/chat'

export const useChatStore = defineStore('chat', () => {
  // State
  const chatRooms = ref([])
  const currentChatRoom = ref(null)
  const messages = ref([])
  const loading = ref(false)
  const error = ref(null)
  const typing = ref({})
  const unsubscribers = ref([])

  // Getters
  const sortedChatRooms = computed(() => 
    chatRooms.value.sort((a, b) => {
      const aTime = a.lastMessageAt?.toDate?.() || new Date(a.lastMessageAt || 0)
      const bTime = b.lastMessageAt?.toDate?.() || new Date(b.lastMessageAt || 0)
      return bTime - aTime
    })
  )

  const unreadCount = computed(() => 
    chatRooms.value.reduce((total, room) => {
      const userId = getCurrentUserId()
      return total + (room.unreadCount?.[userId] || 0)
    }, 0)
  )

  const currentMessages = computed(() => 
    messages.value.map(message => ({
      ...message,
      timestamp: message.createdAt?.toDate?.() || new Date(message.createdAt),
      isOwn: message.senderId === getCurrentUserId()
    }))
  )

  // Helper function to get current user ID
  const getCurrentUserId = () => {
    // This would come from auth store in real implementation
    return 'current_user_id'
  }

  // Actions
  const initializeChatRoom = async (jobId, clientId, freelancerId) => {
    loading.value = true
    error.value = null

    try {
      const result = await ChatService.getChatRoom(jobId, clientId, freelancerId)
      
      if (result.success) {
        currentChatRoom.value = result.chatRoom
        
        // Subscribe to messages
        const unsubscribe = ChatService.subscribeToMessages(
          result.chatRoom.id,
          (newMessages) => {
            messages.value = newMessages
          }
        )
        
        unsubscribers.value.push(unsubscribe)
        
        // Mark as read
        await ChatService.markAsRead(result.chatRoom.id, getCurrentUserId())
        
        return { success: true, chatRoom: result.chatRoom }
      } else {
        error.value = result.error
        return result
      }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const sendMessage = async (message, type = 'text', attachments = []) => {
    if (!currentChatRoom.value) {
      error.value = 'No active chat room'
      return { success: false, error: 'No active chat room' }
    }

    try {
      const result = await ChatService.sendMessage(
        currentChatRoom.value.id,
        getCurrentUserId(),
        message,
        type,
        attachments
      )

      if (!result.success) {
        error.value = result.error
      }

      return result
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    }
  }

  const sendFileMessage = async (files) => {
    if (!currentChatRoom.value) {
      error.value = 'No active chat room'
      return { success: false, error: 'No active chat room' }
    }

    try {
      const result = await ChatService.sendFileMessage(
        currentChatRoom.value.id,
        getCurrentUserId(),
        files
      )

      if (!result.success) {
        error.value = result.error
      }

      return result
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    }
  }

  const loadUserChatRooms = (userId) => {
    const unsubscribe = ChatService.subscribeToUserChatRooms(
      userId,
      (rooms, role) => {
        // Merge rooms from both client and freelancer queries
        const existingIds = new Set(chatRooms.value.map(room => room.id))
        const newRooms = rooms.filter(room => !existingIds.has(room.id))
        chatRooms.value.push(...newRooms)
      }
    )

    unsubscribers.value.push(unsubscribe)
    return unsubscribe
  }

  const editMessage = async (messageId, newMessage) => {
    try {
      const result = await ChatService.editMessage(messageId, newMessage)
      
      if (!result.success) {
        error.value = result.error
      }

      return result
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    }
  }

  const markAsRead = async (chatRoomId) => {
    try {
      const result = await ChatService.markAsRead(chatRoomId, getCurrentUserId())
      
      if (result.success) {
        // Update local state
        const room = chatRooms.value.find(r => r.id === chatRoomId)
        if (room && room.unreadCount) {
          room.unreadCount[getCurrentUserId()] = 0
        }
      }

      return result
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    }
  }

  const setTyping = (chatRoomId, userId, isTyping) => {
    if (!typing.value[chatRoomId]) {
      typing.value[chatRoomId] = {}
    }
    typing.value[chatRoomId][userId] = isTyping

    // Clear typing after 3 seconds
    if (isTyping) {
      setTimeout(() => {
        if (typing.value[chatRoomId]) {
          typing.value[chatRoomId][userId] = false
        }
      }, 3000)
    }
  }

  const getTypingUsers = (chatRoomId) => {
    if (!typing.value[chatRoomId]) return []
    return Object.entries(typing.value[chatRoomId])
      .filter(([userId, isTyping]) => isTyping && userId !== getCurrentUserId())
      .map(([userId]) => userId)
  }

  const clearCurrentChat = () => {
    currentChatRoom.value = null
    messages.value = []
  }

  const clearError = () => {
    error.value = null
  }

  const cleanup = () => {
    // Unsubscribe from all listeners
    unsubscribers.value.forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribe()
      }
    })
    unsubscribers.value = []
    
    // Clear state
    chatRooms.value = []
    currentChatRoom.value = null
    messages.value = []
    typing.value = {}
  }

  // Utility functions
  const formatMessageTime = (timestamp) => {
    if (!timestamp) return ''
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now - date) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    
    return date.toLocaleDateString()
  }

  const getChatRoomName = (chatRoom, currentUserId) => {
    // This would fetch user names from user store in real implementation
    if (chatRoom.clientId === currentUserId) {
      return `Freelancer Chat - Job #${chatRoom.jobId.slice(-6)}`
    } else {
      return `Client Chat - Job #${chatRoom.jobId.slice(-6)}`
    }
  }

  return {
    // State
    chatRooms,
    currentChatRoom,
    messages,
    loading,
    error,
    typing,
    
    // Getters
    sortedChatRooms,
    unreadCount,
    currentMessages,
    
    // Actions
    initializeChatRoom,
    sendMessage,
    sendFileMessage,
    loadUserChatRooms,
    editMessage,
    markAsRead,
    setTyping,
    getTypingUsers,
    clearCurrentChat,
    clearError,
    cleanup,
    formatMessageTime,
    getChatRoomName
  }
})
