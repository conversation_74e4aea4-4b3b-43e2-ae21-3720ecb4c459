// Form validation utilities

export const validators = {
  // Email validation
  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!value) return 'Email is required'
    if (!emailRegex.test(value)) return 'Please enter a valid email address'
    return null
  },

  // Password validation
  password: (value) => {
    if (!value) return 'Password is required'
    if (value.length < 6) return 'Password must be at least 6 characters long'
    if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter'
    if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter'
    if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number'
    return null
  },

  // Simple password validation for MVP
  passwordSimple: (value) => {
    if (!value) return 'Password is required'
    if (value.length < 6) return 'Password must be at least 6 characters long'
    return null
  },

  // Required field validation
  required: (value, fieldName = 'This field') => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return `${fieldName} is required`
    }
    return null
  },

  // Name validation
  name: (value) => {
    if (!value) return 'Name is required'
    if (value.length < 2) return 'Name must be at least 2 characters long'
    if (value.length > 50) return 'Name must be less than 50 characters'
    if (!/^[a-zA-Z\s'-]+$/.test(value)) return 'Name can only contain letters, spaces, hyphens, and apostrophes'
    return null
  },

  // Job title validation
  jobTitle: (value) => {
    if (!value) return 'Job title is required'
    if (value.length < 5) return 'Job title must be at least 5 characters long'
    if (value.length > 100) return 'Job title must be less than 100 characters'
    return null
  },

  // Job description validation
  jobDescription: (value) => {
    if (!value) return 'Job description is required'
    if (value.length < 20) return 'Job description must be at least 20 characters long'
    if (value.length > 5000) return 'Job description must be less than 5000 characters'
    return null
  },

  // Budget validation
  budget: (value) => {
    if (!value) return 'Budget is required'
    const numValue = parseFloat(value)
    if (isNaN(numValue)) return 'Budget must be a valid number'
    if (numValue < 5) return 'Minimum budget is $5'
    if (numValue > 100000) return 'Maximum budget is $100,000'
    return null
  },

  // URL validation
  url: (value) => {
    if (!value) return null // URL is optional in most cases
    try {
      new URL(value)
      return null
    } catch {
      return 'Please enter a valid URL'
    }
  },

  // Phone number validation (basic)
  phone: (value) => {
    if (!value) return null // Phone is usually optional
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/
    if (!phoneRegex.test(value)) return 'Please enter a valid phone number'
    return null
  },

  // Skills validation
  skills: (value) => {
    if (!value) return 'At least one skill is required'
    const skillsArray = value.split(',').map(skill => skill.trim()).filter(Boolean)
    if (skillsArray.length === 0) return 'At least one skill is required'
    if (skillsArray.length > 20) return 'Maximum 20 skills allowed'
    return null
  },

  // File validation
  file: (file, options = {}) => {
    const { maxSize = 10 * 1024 * 1024, allowedTypes = [] } = options // 10MB default
    
    if (!file) return 'File is required'
    
    if (file.size > maxSize) {
      return `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`
    }
    
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
    }
    
    return null
  }
}

// Input sanitization utilities
export const sanitizers = {
  // Remove HTML tags and dangerous characters
  text: (value) => {
    if (!value) return ''
    return value
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[<>]/g, '') // Remove remaining < >
      .trim()
  },

  // Sanitize email
  email: (value) => {
    if (!value) return ''
    return value.toLowerCase().trim()
  },

  // Sanitize URL
  url: (value) => {
    if (!value) return ''
    let url = value.trim()
    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    return url
  },

  // Sanitize number
  number: (value) => {
    if (!value) return 0
    const num = parseFloat(value)
    return isNaN(num) ? 0 : num
  },

  // Sanitize skills array
  skills: (value) => {
    if (!value) return []
    return value
      .split(',')
      .map(skill => sanitizers.text(skill))
      .filter(Boolean)
      .slice(0, 20) // Limit to 20 skills
  }
}

// Form validation helper
export class FormValidator {
  constructor(rules = {}) {
    this.rules = rules
    this.errors = {}
  }

  validate(data) {
    this.errors = {}
    let isValid = true

    for (const [field, fieldRules] of Object.entries(this.rules)) {
      const value = data[field]
      
      for (const rule of fieldRules) {
        let error = null
        
        if (typeof rule === 'function') {
          error = rule(value)
        } else if (typeof rule === 'string' && validators[rule]) {
          error = validators[rule](value)
        } else if (typeof rule === 'object' && rule.validator) {
          error = rule.validator(value, rule.options)
        }
        
        if (error) {
          this.errors[field] = error
          isValid = false
          break // Stop at first error for this field
        }
      }
    }

    return isValid
  }

  getError(field) {
    return this.errors[field] || null
  }

  hasError(field) {
    return !!this.errors[field]
  }

  getAllErrors() {
    return this.errors
  }

  clearErrors() {
    this.errors = {}
  }

  clearError(field) {
    delete this.errors[field]
  }
}

// Spam detection utilities
export const spamDetection = {
  // Check for suspicious patterns in text
  checkText: (text) => {
    if (!text) return { isSpam: false, confidence: 0 }

    const spamPatterns = [
      /\b(viagra|cialis|casino|lottery|winner|congratulations)\b/i,
      /\b(click here|act now|limited time|urgent|free money)\b/i,
      /\$\$\$|!!!/,
      /\b\d{10,}\b/, // Long numbers (phone/credit card)
      /(http|https):\/\/[^\s]+/g // Multiple URLs
    ]

    let spamScore = 0
    const matches = []

    spamPatterns.forEach((pattern, index) => {
      const match = text.match(pattern)
      if (match) {
        spamScore += 1
        matches.push({ pattern: index, match: match[0] })
      }
    })

    // Check for excessive capitalization
    const capsRatio = (text.match(/[A-Z]/g) || []).length / text.length
    if (capsRatio > 0.5) {
      spamScore += 1
      matches.push({ pattern: 'excessive_caps', match: 'Too many capital letters' })
    }

    // Check for repeated characters
    if (/(.)\1{4,}/.test(text)) {
      spamScore += 1
      matches.push({ pattern: 'repeated_chars', match: 'Repeated characters' })
    }

    const confidence = Math.min(spamScore / 3, 1) // Normalize to 0-1
    const isSpam = confidence > 0.6

    return { isSpam, confidence, matches }
  },

  // Check for suspicious user behavior
  checkUserBehavior: (userData) => {
    let suspiciousScore = 0
    const flags = []

    // Check for suspicious email patterns
    if (userData.email && /\d{5,}/.test(userData.email)) {
      suspiciousScore += 1
      flags.push('suspicious_email')
    }

    // Check for generic names
    if (userData.name && /^(test|user|admin|demo)\d*$/i.test(userData.name)) {
      suspiciousScore += 1
      flags.push('generic_name')
    }

    // Check for rapid account creation (would need timestamp comparison)
    // This would be implemented with actual user creation timestamps

    return {
      isSuspicious: suspiciousScore > 0,
      score: suspiciousScore,
      flags
    }
  }
}

// Rate limiting utilities (client-side tracking)
export class RateLimiter {
  constructor(maxAttempts = 5, windowMs = 15 * 60 * 1000) { // 5 attempts per 15 minutes
    this.maxAttempts = maxAttempts
    this.windowMs = windowMs
    this.attempts = new Map()
  }

  isAllowed(key) {
    const now = Date.now()
    const userAttempts = this.attempts.get(key) || []
    
    // Remove old attempts outside the window
    const validAttempts = userAttempts.filter(timestamp => now - timestamp < this.windowMs)
    
    if (validAttempts.length >= this.maxAttempts) {
      return false
    }

    // Record this attempt
    validAttempts.push(now)
    this.attempts.set(key, validAttempts)
    
    return true
  }

  getRemainingAttempts(key) {
    const now = Date.now()
    const userAttempts = this.attempts.get(key) || []
    const validAttempts = userAttempts.filter(timestamp => now - timestamp < this.windowMs)
    
    return Math.max(0, this.maxAttempts - validAttempts.length)
  }

  getTimeUntilReset(key) {
    const userAttempts = this.attempts.get(key) || []
    if (userAttempts.length === 0) return 0
    
    const oldestAttempt = Math.min(...userAttempts)
    const resetTime = oldestAttempt + this.windowMs
    
    return Math.max(0, resetTime - Date.now())
  }
}

export default {
  validators,
  sanitizers,
  FormValidator,
  spamDetection,
  RateLimiter
}
