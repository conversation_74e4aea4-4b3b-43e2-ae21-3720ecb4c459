<template>
  <div>
    <!-- Hero Section -->
    <section class="hero bg-red-600 text-white py-20">
      <div class="container mx-auto px-4 text-center">
        <h1 class="text-5xl font-bold mb-4">Gignify: Real Work. Real Talent. Recognized.</h1>
        <p class="text-xl mb-8">Supercharge your freelancing experience with our gamified platform.</p>
        <div class="flex justify-center space-x-4">
          <router-link
            to="/signup"
            class="bg-white text-red-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-200 transition-colors"
          >
            Join as Freelancer
          </router-link>
          <router-link
            to="/signup"
            class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-red-600 transition-colors"
          >
            Hire Talent
          </router-link>
        </div>

        <!-- Stats -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div>
            <div class="text-3xl font-bold">{{ stats.totalFreelancers }}+</div>
            <div class="text-red-200">Talented Freelancers</div>
          </div>
          <div>
            <div class="text-3xl font-bold">{{ stats.totalJobs }}+</div>
            <div class="text-red-200">Jobs Completed</div>
          </div>
          <div>
            <div class="text-3xl font-bold">${{ stats.totalEarnings }}k+</div>
            <div class="text-red-200">Paid to Freelancers</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Categories Grid -->
    <section class="categories py-16">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold mb-6 text-center">Job Categories</h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
          <router-link
            v-for="category in jobCategories"
            :key="category.id"
            :to="`/leaderboards?category=${category.id}`"
            class="category-card p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow text-center group cursor-pointer"
          >
            <div class="text-3xl mb-2">{{ category.icon }}</div>
            <div class="font-medium text-gray-900 group-hover:text-red-600">{{ category.name }}</div>
            <div class="text-sm text-gray-500 mt-1">{{ getCategoryJobCount(category.id) }} jobs</div>
          </router-link>
        </div>
      </div>
    </section>

    <!-- Freelancer of the Month -->
    <section v-if="freelancerOfTheMonth" class="py-16 bg-gradient-to-r from-yellow-400 to-orange-500">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold text-white mb-8">🏆 Freelancer of the Month</h2>
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
          <div class="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-2xl font-bold text-white">{{ freelancerOfTheMonth.name.charAt(0) }}</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">{{ freelancerOfTheMonth.name }}</h3>
          <p class="text-gray-600 mb-4">{{ freelancerOfTheMonth.category || 'Multi-skilled' }}</p>
          <div class="flex justify-center space-x-6 text-sm">
            <div>
              <div class="font-bold text-yellow-600">{{ freelancerOfTheMonth.xp || 0 }}</div>
              <div class="text-gray-500">XP</div>
            </div>
            <div>
              <div class="font-bold text-yellow-600">{{ freelancerOfTheMonth.tier || 1 }}</div>
              <div class="text-gray-500">Tier</div>
            </div>
            <div>
              <div class="font-bold text-yellow-600">{{ freelancerOfTheMonth.completedJobs || 0 }}</div>
              <div class="text-gray-500">Jobs</div>
            </div>
          </div>
          <router-link
            :to="`/freelancer/profile/${freelancerOfTheMonth.id}`"
            class="mt-4 inline-block bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition-colors"
          >
            View Profile
          </router-link>
        </div>
      </div>
    </section>

    <!-- Top Champs by Category -->
    <section class="leaderboards py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Top Champs by Category</h2>
          <p class="text-gray-600">Our highest-performing freelancers across different skills</p>
        </div>

        <div v-if="loadingLeaderboards" class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
          <p class="text-gray-500 mt-2">Loading leaderboards...</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="category in featuredCategories"
            :key="category.id"
            class="leaderboard-card bg-white rounded-lg shadow p-6"
          >
            <div class="flex items-center mb-4">
              <span class="text-2xl mr-3">{{ category.icon }}</span>
              <h3 class="font-semibold text-lg">{{ category.name }}</h3>
            </div>

            <div v-if="getCategoryLeaderboard(category.id).length === 0" class="text-center py-4">
              <p class="text-gray-500 text-sm">No freelancers yet</p>
            </div>

            <ul v-else class="space-y-2">
              <li
                v-for="(freelancer, index) in getCategoryLeaderboard(category.id).slice(0, 3)"
                :key="freelancer.id"
                class="flex items-center justify-between"
              >
                <div class="flex items-center">
                  <span :class="[
                    'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white mr-3',
                    index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-yellow-600'
                  ]">
                    {{ index + 1 }}
                  </span>
                  <span class="font-medium">{{ freelancer.name }}</span>
                </div>
                <span class="text-sm text-gray-500">{{ freelancer.xp || 0 }} XP</span>
              </li>
            </ul>

            <router-link
              :to="`/leaderboards?category=${category.id}`"
              class="mt-4 block text-center text-red-600 hover:text-red-500 text-sm font-medium"
            >
              View Full Leaderboard →
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- New Talents -->
    <section v-if="newTalents.length > 0" class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">🌟 New Talents to Watch</h2>
          <p class="text-gray-600">Rising stars who joined recently and are already making an impact</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="talent in newTalents.slice(0, 6)"
            :key="talent.id"
            class="bg-white rounded-lg shadow p-6 text-center"
          >
            <div class="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span class="text-xl font-bold text-white">{{ talent.name.charAt(0) }}</span>
            </div>
            <h3 class="font-semibold text-lg mb-2">{{ talent.name }}</h3>
            <p class="text-gray-600 text-sm mb-3">{{ talent.category || 'Multi-skilled' }}</p>
            <div class="flex justify-center space-x-4 text-sm mb-4">
              <div>
                <div class="font-bold text-blue-600">{{ talent.xp || 0 }}</div>
                <div class="text-gray-500">XP</div>
              </div>
              <div>
                <div class="font-bold text-blue-600">{{ talent.completedJobs || 0 }}</div>
                <div class="text-gray-500">Jobs</div>
              </div>
            </div>
            <router-link
              :to="`/freelancer/profile/${talent.id}`"
              class="inline-block bg-blue-600 text-white px-4 py-2 rounded-full text-sm hover:bg-blue-700 transition-colors"
            >
              View Profile
            </router-link>
          </div>
        </div>

        <div class="text-center mt-8">
          <router-link
            to="/new-talents"
            class="inline-block bg-red-600 text-white px-6 py-3 rounded-full hover:bg-red-700 transition-colors"
          >
            Discover More New Talents
          </router-link>
        </div>
      </div>
    </section>

    <!-- Client Testimonials -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">What Our Clients Say</h2>
          <p class="text-gray-600">Real feedback from satisfied clients</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="testimonial in testimonials"
            :key="testimonial.id"
            class="bg-white rounded-lg shadow p-6"
          >
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
                <span class="text-white font-bold">{{ testimonial.clientName.charAt(0) }}</span>
              </div>
              <div>
                <h4 class="font-semibold">{{ testimonial.clientName }}</h4>
                <p class="text-sm text-gray-500">{{ testimonial.company }}</p>
              </div>
            </div>
            <p class="text-gray-700 mb-4">"{{ testimonial.feedback }}"</p>
            <div class="flex items-center">
              <div class="flex text-yellow-400 mr-2">
                <span v-for="i in 5" :key="i">
                  {{ i <= testimonial.rating ? '★' : '☆' }}
                </span>
              </div>
              <span class="text-sm text-gray-500">{{ testimonial.rating }}/5</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-red-600 text-white">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
        <p class="text-xl mb-8">Join thousands of freelancers and clients on Gignify</p>
        <div class="flex justify-center space-x-4">
          <router-link
            to="/signup"
            class="bg-white text-red-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-200 transition-colors"
          >
            Sign Up Now
          </router-link>
          <router-link
            to="/leaderboards"
            class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-red-600 transition-colors"
          >
            View Leaderboards
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useJobsStore } from '../stores/jobs'
import { useLeaderboardStore } from '../stores/leaderboard'

const jobsStore = useJobsStore()
const leaderboardStore = useLeaderboardStore()

const loadingLeaderboards = ref(true)

// Mock stats for homepage
const stats = ref({
  totalFreelancers: 1247,
  totalJobs: 3892,
  totalEarnings: 485
})

// Job categories from store
const jobCategories = computed(() => jobsStore.jobCategories)

// Featured categories for leaderboards (first 3)
const featuredCategories = computed(() => jobCategories.value.slice(0, 3))

// Leaderboard data
const freelancerOfTheMonth = computed(() => leaderboardStore.freelancerOfTheMonth)
const newTalents = computed(() => leaderboardStore.newTalents)

// Mock testimonials
const testimonials = ref([
  {
    id: 1,
    clientName: 'Sarah Johnson',
    company: 'TechStart Inc.',
    feedback: 'Found an amazing developer through Gignify. The gamification system really helps identify top talent!',
    rating: 5
  },
  {
    id: 2,
    clientName: 'Mike Chen',
    company: 'Design Studio Pro',
    feedback: 'The quality of freelancers on Gignify is outstanding. Love the tier system and badges.',
    rating: 5
  },
  {
    id: 3,
    clientName: 'Emily Rodriguez',
    company: 'Marketing Plus',
    feedback: 'Quick turnaround, excellent communication, and fair pricing. Highly recommend!',
    rating: 4
  }
])

// Helper functions
const getCategoryJobCount = (categoryId) => {
  // Mock job counts per category
  const counts = {
    'ai': 156,
    'dev': 342,
    'design': 198,
    'writing': 127,
    'marketing': 89,
    'video': 76,
    'music': 45,
    'business': 112
  }
  return counts[categoryId] || 0
}

const getCategoryLeaderboard = (categoryId) => {
  return leaderboardStore.categoryLeaderboards[categoryId] || []
}

onMounted(async () => {
  try {
    await leaderboardStore.initializeLeaderboards()
  } catch (error) {
    console.error('Error loading leaderboards:', error)
  } finally {
    loadingLeaderboards.value = false
  }
})
</script>

<style scoped>
.category-card {
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-2px);
}

.hero {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.leaderboard-card {
  transition: all 0.3s ease;
}

.leaderboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>
