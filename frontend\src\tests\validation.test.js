import { describe, it, expect } from 'vitest'
import { validators, sanitizers, spamDetection, FormValidator } from '../utils/validation'

describe('Validators', () => {
  describe('email validator', () => {
    it('should validate correct email addresses', () => {
      expect(validators.email('<EMAIL>')).toBeNull()
      expect(validators.email('<EMAIL>')).toBeNull()
    })

    it('should reject invalid email addresses', () => {
      expect(validators.email('')).toBe('Email is required')
      expect(validators.email('invalid-email')).toBe('Please enter a valid email address')
      expect(validators.email('test@')).toBe('Please enter a valid email address')
      expect(validators.email('@domain.com')).toBe('Please enter a valid email address')
    })
  })

  describe('password validator', () => {
    it('should validate strong passwords', () => {
      expect(validators.password('Password123')).toBeNull()
      expect(validators.password('MySecure1')).toBeNull()
    })

    it('should reject weak passwords', () => {
      expect(validators.password('')).toBe('Password is required')
      expect(validators.password('12345')).toBe('Password must be at least 6 characters long')
      expect(validators.password('password')).toBe('Password must contain at least one uppercase letter')
      expect(validators.password('PASSWORD')).toBe('Password must contain at least one lowercase letter')
      expect(validators.password('Password')).toBe('Password must contain at least one number')
    })
  })

  describe('name validator', () => {
    it('should validate correct names', () => {
      expect(validators.name('John Doe')).toBeNull()
      expect(validators.name("O'Connor")).toBeNull()
      expect(validators.name('Mary-Jane')).toBeNull()
    })

    it('should reject invalid names', () => {
      expect(validators.name('')).toBe('Name is required')
      expect(validators.name('A')).toBe('Name must be at least 2 characters long')
      expect(validators.name('John123')).toBe('Name can only contain letters, spaces, hyphens, and apostrophes')
    })
  })

  describe('budget validator', () => {
    it('should validate correct budgets', () => {
      expect(validators.budget('100')).toBeNull()
      expect(validators.budget('50000')).toBeNull()
    })

    it('should reject invalid budgets', () => {
      expect(validators.budget('')).toBe('Budget is required')
      expect(validators.budget('abc')).toBe('Budget must be a valid number')
      expect(validators.budget('4')).toBe('Minimum budget is $5')
      expect(validators.budget('150000')).toBe('Maximum budget is $100,000')
    })
  })
})

describe('Sanitizers', () => {
  describe('text sanitizer', () => {
    it('should remove HTML tags', () => {
      expect(sanitizers.text('<script>alert("xss")</script>Hello')).toBe('Hello')
      expect(sanitizers.text('<b>Bold</b> text')).toBe('Bold text')
    })

    it('should remove dangerous characters', () => {
      expect(sanitizers.text('Hello<>World')).toBe('HelloWorld')
      expect(sanitizers.text('  Trimmed  ')).toBe('Trimmed')
    })
  })

  describe('email sanitizer', () => {
    it('should normalize email addresses', () => {
      expect(sanitizers.email('  <EMAIL>  ')).toBe('<EMAIL>')
      expect(sanitizers.email('<EMAIL>')).toBe('<EMAIL>')
    })
  })

  describe('skills sanitizer', () => {
    it('should parse and clean skills', () => {
      expect(sanitizers.skills('JavaScript, React, Node.js')).toEqual(['JavaScript', 'React', 'Node.js'])
      expect(sanitizers.skills('  HTML  ,  CSS  ,  ')).toEqual(['HTML', 'CSS'])
    })

    it('should limit skills to 20', () => {
      const manySkills = Array.from({ length: 25 }, (_, i) => `Skill${i + 1}`).join(', ')
      const result = sanitizers.skills(manySkills)
      expect(result).toHaveLength(20)
    })
  })
})

describe('Spam Detection', () => {
  describe('text spam detection', () => {
    it('should detect spam patterns', () => {
      const spamText = 'URGENT!!! Click here to win $$$'
      const result = spamDetection.checkText(spamText)
      expect(result.isSpam).toBe(true)
      expect(result.confidence).toBeGreaterThan(0.5)
    })

    it('should allow normal text', () => {
      const normalText = 'I need help building a website for my business'
      const result = spamDetection.checkText(normalText)
      expect(result.isSpam).toBe(false)
    })

    it('should detect excessive capitalization', () => {
      const capsText = 'THIS IS ALL CAPS TEXT'
      const result = spamDetection.checkText(capsText)
      expect(result.isSpam).toBe(true)
    })
  })

  describe('user behavior detection', () => {
    it('should detect suspicious email patterns', () => {
      const userData = { email: '<EMAIL>', name: 'John Doe' }
      const result = spamDetection.checkUserBehavior(userData)
      expect(result.isSuspicious).toBe(true)
      expect(result.flags).toContain('suspicious_email')
    })

    it('should detect generic names', () => {
      const userData = { email: '<EMAIL>', name: 'test123' }
      const result = spamDetection.checkUserBehavior(userData)
      expect(result.isSuspicious).toBe(true)
      expect(result.flags).toContain('generic_name')
    })
  })
})

describe('FormValidator', () => {
  it('should validate forms with multiple rules', () => {
    const validator = new FormValidator({
      email: ['email'],
      name: ['name'],
      password: ['passwordSimple']
    })

    const validData = {
      email: '<EMAIL>',
      name: 'John Doe',
      password: 'password123'
    }

    expect(validator.validate(validData)).toBe(true)
    expect(validator.getAllErrors()).toEqual({})
  })

  it('should collect validation errors', () => {
    const validator = new FormValidator({
      email: ['email'],
      name: ['name']
    })

    const invalidData = {
      email: 'invalid-email',
      name: 'A'
    }

    expect(validator.validate(invalidData)).toBe(false)
    expect(validator.hasError('email')).toBe(true)
    expect(validator.hasError('name')).toBe(true)
  })

  it('should clear errors', () => {
    const validator = new FormValidator({
      email: ['email']
    })

    validator.validate({ email: 'invalid' })
    expect(validator.hasError('email')).toBe(true)

    validator.clearErrors()
    expect(validator.hasError('email')).toBe(false)
  })
})
