<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 bg-red-600 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-xl">G</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <router-link to="/login" class="font-medium text-red-600 hover:text-red-500">
            sign in to your existing account
          </router-link>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleSignup">
        <!-- Role Selection -->
        <div>
          <label class="text-base font-medium text-gray-900">I want to:</label>
          <div class="mt-4 space-y-4">
            <div class="flex items-center">
              <input
                id="freelancer"
                v-model="form.role"
                name="role"
                type="radio"
                value="freelancer"
                class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
              />
              <label for="freelancer" class="ml-3 block text-sm font-medium text-gray-700">
                Find work as a freelancer
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="client"
                v-model="form.role"
                name="role"
                type="radio"
                value="client"
                class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
              />
              <label for="client" class="ml-3 block text-sm font-medium text-gray-700">
                Hire freelancers for my projects
              </label>
            </div>
          </div>
        </div>

        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="name" class="sr-only">Full name</label>
            <input
              id="name"
              v-model="form.name"
              name="name"
              type="text"
              autocomplete="name"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm"
              placeholder="Full name"
            />
          </div>
          <div>
            <label for="email" class="sr-only">Email address</label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm"
              placeholder="Email address"
            />
          </div>
          <div>
            <label for="password" class="sr-only">Password</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="new-password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm"
              placeholder="Password"
            />
          </div>
          <div>
            <label for="confirmPassword" class="sr-only">Confirm Password</label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              name="confirmPassword"
              type="password"
              autocomplete="new-password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm"
              placeholder="Confirm password"
            />
          </div>
        </div>

        <div class="flex items-center">
          <input
            id="agree-terms"
            v-model="form.agreeTerms"
            name="agree-terms"
            type="checkbox"
            required
            class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
          />
          <label for="agree-terms" class="ml-2 block text-sm text-gray-900">
            I agree to the
            <a href="#" class="text-red-600 hover:text-red-500">Terms of Service</a>
            and
            <a href="#" class="text-red-600 hover:text-red-500">Privacy Policy</a>
          </label>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
            {{ loading ? 'Creating account...' : 'Create account' }}
          </button>
        </div>

        <div v-if="error" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ error }}
              </h3>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useAppStore } from '../stores/app'
import { securityService } from '../services/security'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const form = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'freelancer',
  agreeTerms: false
})

const loading = computed(() => authStore.loading)
const error = computed(() => authStore.error)

const handleSignup = async () => {
  // Check rate limiting
  const rateLimitCheck = securityService.checkRateLimit('signup', form.value.email)
  if (!rateLimitCheck.allowed) {
    const resetTime = rateLimitCheck.resetTime
    const resetString = resetTime ? ` Try again at ${resetTime.toLocaleTimeString()}` : ''
    appStore.showError(`Too many signup attempts.${resetString}`)
    return
  }

  // Basic validation
  if (!form.value.agreeTerms) {
    appStore.showError('Please agree to the terms and conditions')
    return
  }

  if (form.value.password !== form.value.confirmPassword) {
    appStore.showError('Passwords do not match')
    return
  }

  // Comprehensive validation and sanitization
  const validation = securityService.validateUserRegistration(form.value)

  if (!validation.isValid) {
    const firstError = Object.values(validation.errors)[0]
    appStore.showError(firstError)
    return
  }

  const userData = {
    name: validation.sanitized.name,
    role: validation.sanitized.role
  }

  const result = await authStore.register(validation.sanitized.email, validation.sanitized.password, userData)

  if (result.success) {
    appStore.showSuccess('Account created successfully!')
    router.push('/onboarding')
  }
}
</script>
