import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

// Views
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Signup from '../views/Signup.vue'
import Onboarding from '../views/Onboarding.vue'

// Lazy load dashboard components
const ClientDashboard = () => import('../views/client/Dashboard.vue')
const ClientJobNew = () => import('../views/client/JobNew.vue')
const ClientJobDetail = () => import('../views/client/JobDetail.vue')

const FreelancerDashboard = () => import('../views/freelancer/Dashboard.vue')
const FreelancerJobDetail = () => import('../views/freelancer/JobDetail.vue')
const FreelancerProfile = () => import('../views/freelancer/Profile.vue')

const Leaderboards = () => import('../views/Leaderboards.vue')
const FreelancerOfTheMonth = () => import('../views/FreelancerOfTheMonth.vue')
const NewTalents = () => import('../views/NewTalents.vue')
const ProfileEdit = () => import('../views/ProfileEdit.vue')
const ComingSoon = () => import('../views/ComingSoon.vue')

// Admin components
const AdminDashboard = () => import('../views/admin/Dashboard.vue')

const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/login', name: 'Login', component: Login },
  { path: '/signup', name: 'Signup', component: Signup },
  {
    path: '/onboarding',
    name: 'Onboarding',
    component: Onboarding,
    meta: { requiresAuth: true }
  },

  // Client routes
  {
    path: '/client/dashboard',
    name: 'ClientDashboard',
    component: ClientDashboard,
    meta: { requiresAuth: true, role: 'client' }
  },
  {
    path: '/client/job/new',
    name: 'ClientJobNew',
    component: ClientJobNew,
    meta: { requiresAuth: true, role: 'client' }
  },
  {
    path: '/client/job/:id',
    name: 'ClientJobDetail',
    component: ClientJobDetail,
    meta: { requiresAuth: true, role: 'client' }
  },

  // Freelancer routes
  {
    path: '/freelancer/dashboard',
    name: 'FreelancerDashboard',
    component: FreelancerDashboard,
    meta: { requiresAuth: true, role: 'freelancer' }
  },
  {
    path: '/freelancer/job/:id',
    name: 'FreelancerJobDetail',
    component: FreelancerJobDetail,
    meta: { requiresAuth: true, role: 'freelancer' }
  },
  {
    path: '/freelancer/profile/:username',
    name: 'FreelancerProfile',
    component: FreelancerProfile
  },

  // Public routes
  { path: '/leaderboards', name: 'Leaderboards', component: Leaderboards },
  { path: '/freelancer-of-the-month', name: 'FreelancerOfTheMonth', component: FreelancerOfTheMonth },
  { path: '/new-talents', name: 'NewTalents', component: NewTalents },

  // Protected routes
  {
    path: '/profile/edit',
    name: 'ProfileEdit',
    component: ProfileEdit,
    meta: { requiresAuth: true }
  },

  // Admin routes
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: { requiresAuth: true, role: 'admin' }
  },

  // Coming soon routes
  { path: '/payment/checkout', name: 'PaymentCheckout', component: ComingSoon },
  { path: '/payment/success', name: 'PaymentSuccess', component: ComingSoon },
  { path: '/payment/failure', name: 'PaymentFailure', component: ComingSoon },
  { path: '/coming-soon', name: 'ComingSoon', component: ComingSoon },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Navigation guards
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()

  // Wait for auth to initialize if it's still loading
  if (authStore.loading) {
    await new Promise(resolve => {
      const unwatch = authStore.$subscribe(() => {
        if (!authStore.loading) {
          unwatch()
          resolve()
        }
      })
    })
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiredRole = to.meta.role

  if (requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (requiredRole && authStore.userRole !== requiredRole) {
    // Redirect to appropriate dashboard if wrong role
    if (authStore.isClient) {
      next('/client/dashboard')
    } else if (authStore.isFreelancer) {
      next('/freelancer/dashboard')
    } else {
      next('/')
    }
  } else {
    next()
  }
})

export default router
