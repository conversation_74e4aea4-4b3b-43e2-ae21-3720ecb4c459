import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import StripeService from '../services/stripe'

export const usePaymentsStore = defineStore('payments', () => {
  // State
  const payments = ref([])
  const currentPayment = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const stripeAccount = ref(null)

  // Getters
  const pendingPayments = computed(() => 
    payments.value.filter(payment => payment.status === 'pending')
  )
  
  const completedPayments = computed(() => 
    payments.value.filter(payment => payment.status === 'succeeded')
  )

  const totalEarnings = computed(() => 
    completedPayments.value.reduce((total, payment) => total + payment.freelancerAmount, 0)
  )

  // Actions
  const createPaymentSession = async (jobData, freelancerData) => {
    loading.value = true
    error.value = null

    try {
      const session = await StripeService.createCheckoutSession(jobData, freelancerData)
      
      // Store payment record
      const payment = {
        id: session.id,
        jobId: jobData.id,
        jobTitle: jobData.title,
        clientId: jobData.postedBy,
        freelancerId: freelancerData.uid,
        freelancerName: freelancerData.name,
        amount: parseFloat(jobData.budget),
        platformFee: parseFloat(jobData.budget) * 0.05,
        freelancerAmount: parseFloat(jobData.budget) * 0.95,
        status: 'pending',
        sessionUrl: session.url,
        createdAt: new Date()
      }

      payments.value.push(payment)
      currentPayment.value = payment

      return { success: true, session }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const redirectToCheckout = async (sessionId) => {
    loading.value = true
    error.value = null

    try {
      const result = await StripeService.redirectToCheckout(sessionId)
      
      if (result.error) {
        error.value = result.error
        return { success: false, error: result.error }
      }

      return { success: true }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const setupStripeAccount = async (userData) => {
    loading.value = true
    error.value = null

    try {
      const result = await StripeService.createConnectAccount(userData)
      
      if (result.success) {
        stripeAccount.value = {
          id: result.accountId,
          status: 'pending',
          onboardingUrl: result.onboardingUrl,
          chargesEnabled: false,
          payoutsEnabled: false
        }

        return { success: true, account: stripeAccount.value }
      } else {
        error.value = result.error
        return { success: false, error: result.error }
      }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const checkAccountStatus = async (accountId) => {
    loading.value = true
    error.value = null

    try {
      const result = await StripeService.getAccountStatus(accountId)
      
      if (result.success) {
        if (stripeAccount.value) {
          stripeAccount.value.status = result.status
          stripeAccount.value.chargesEnabled = result.chargesEnabled
          stripeAccount.value.payoutsEnabled = result.payoutsEnabled
          stripeAccount.value.requirements = result.requirements
        }

        return { success: true, status: result }
      } else {
        error.value = result.error
        return { success: false, error: result.error }
      }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const processRefund = async (paymentId, amount = null) => {
    loading.value = true
    error.value = null

    try {
      const payment = payments.value.find(p => p.id === paymentId)
      if (!payment) {
        throw new Error('Payment not found')
      }

      const result = await StripeService.processRefund(payment.paymentIntentId, amount)
      
      if (result.success) {
        // Update payment status
        payment.status = 'refunded'
        payment.refundId = result.refundId
        payment.refundAmount = amount || payment.amount
        payment.refundedAt = new Date()

        return { success: true, refund: result }
      } else {
        error.value = result.error
        return { success: false, error: result.error }
      }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const calculateFees = (amount) => {
    return StripeService.calculatePlatformFee(amount)
  }

  const formatAmount = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const updatePaymentStatus = (paymentId, status, metadata = {}) => {
    const payment = payments.value.find(p => p.id === paymentId)
    if (payment) {
      payment.status = status
      payment.updatedAt = new Date()
      
      if (metadata.paymentIntentId) {
        payment.paymentIntentId = metadata.paymentIntentId
      }
      
      if (status === 'succeeded') {
        payment.completedAt = new Date()
      }
    }
  }

  const getPaymentHistory = (userId, role = 'freelancer') => {
    if (role === 'freelancer') {
      return payments.value.filter(p => p.freelancerId === userId)
    } else {
      return payments.value.filter(p => p.clientId === userId)
    }
  }

  const getPaymentById = (paymentId) => {
    return payments.value.find(p => p.id === paymentId)
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentPayment = () => {
    currentPayment.value = null
  }

  // Mock data for development
  const loadMockPayments = () => {
    payments.value = [
      {
        id: 'pay_mock_1',
        jobId: 'job_1',
        jobTitle: 'Build a React Website',
        clientId: 'client_1',
        freelancerId: 'freelancer_1',
        freelancerName: 'John Doe',
        amount: 500,
        platformFee: 25,
        freelancerAmount: 475,
        status: 'succeeded',
        completedAt: new Date(Date.now() - 86400000), // 1 day ago
        createdAt: new Date(Date.now() - *********) // 2 days ago
      },
      {
        id: 'pay_mock_2',
        jobId: 'job_2',
        jobTitle: 'Logo Design',
        clientId: 'client_2',
        freelancerId: 'freelancer_1',
        freelancerName: 'John Doe',
        amount: 200,
        platformFee: 10,
        freelancerAmount: 190,
        status: 'pending',
        createdAt: new Date(Date.now() - 3600000) // 1 hour ago
      }
    ]
  }

  return {
    // State
    payments,
    currentPayment,
    loading,
    error,
    stripeAccount,
    
    // Getters
    pendingPayments,
    completedPayments,
    totalEarnings,
    
    // Actions
    createPaymentSession,
    redirectToCheckout,
    setupStripeAccount,
    checkAccountStatus,
    processRefund,
    calculateFees,
    formatAmount,
    updatePaymentStatus,
    getPaymentHistory,
    getPaymentById,
    clearError,
    clearCurrentPayment,
    loadMockPayments
  }
})
