<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <div v-if="loading" class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
        <p class="mt-2 text-gray-500">Loading job details...</p>
      </div>

      <div v-else-if="!job" class="text-center">
        <h1 class="text-2xl font-bold text-gray-900">Job not found</h1>
        <router-link to="/client/dashboard" class="mt-4 text-red-600 hover:text-red-500">
          Back to Dashboard
        </router-link>
      </div>

      <div v-else class="space-y-8">
        <!-- Header -->
        <div class="flex justify-between items-start">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ job.title }}</h1>
            <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
              <span class="capitalize">{{ job.category }}</span>
              <span>•</span>
              <span>${{ job.budget }}</span>
              <span>•</span>
              <span>{{ formatDate(job.createdAt) }}</span>
            </div>
          </div>
          <span :class="[
            'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
            job.status === 'open' ? 'bg-green-100 text-green-800' :
            job.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          ]">
            {{ job.status }}
          </span>
        </div>

        <!-- Job Details -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Job Description</h2>
          <p class="text-gray-700 whitespace-pre-wrap">{{ job.description }}</p>

          <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-sm font-medium text-gray-900">Required Skills</h3>
              <div class="mt-2 flex flex-wrap gap-2">
                <span
                  v-for="skill in job.skills"
                  :key="skill"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ skill }}
                </span>
              </div>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-900">Deadline</h3>
              <p class="mt-2 text-sm text-gray-700">{{ formatDate(job.deadline) }}</p>
            </div>
          </div>
        </div>

        <!-- Proposals -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">
              Proposals ({{ proposalCount }})
            </h2>
          </div>

          <div v-if="proposalCount === 0" class="p-6 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No proposals yet</h3>
            <p class="mt-1 text-sm text-gray-500">Freelancers will submit proposals for your job.</p>
          </div>

          <div v-else class="divide-y divide-gray-200">
            <div
              v-for="(proposal, freelancerId) in job.proposals"
              :key="freelancerId"
              class="p-6"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="text-lg font-medium text-gray-900">{{ proposal.freelancerName || 'Freelancer' }}</h3>
                  <p class="mt-1 text-sm text-gray-500">Proposed: ${{ proposal.price }}</p>
                  <p class="mt-2 text-gray-700">{{ proposal.coverLetter }}</p>

                  <div class="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                    <span>AI Score: {{ proposal.aiScore || 0 }}/100</span>
                    <span>•</span>
                    <span>{{ formatDate(proposal.submittedAt) }}</span>
                  </div>
                </div>

                <div class="flex-shrink-0 ml-4">
                  <span :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    proposal.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    proposal.status === 'accepted' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ proposal.status }}
                  </span>
                </div>
              </div>

              <div v-if="proposal.status === 'pending'" class="mt-4 flex space-x-3">
                <button
                  @click="acceptProposal(freelancerId, proposal)"
                  :disabled="processing"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                >
                  {{ processing ? 'Processing...' : 'Accept & Pay' }}
                </button>
                <button
                  @click="rejectProposal(freelancerId)"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Decline
                </button>
              </div>

              <div v-else-if="proposal.status === 'accepted'" class="mt-4 p-3 bg-green-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-sm font-medium text-green-800">Proposal Accepted - Payment Processed</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useJobsStore } from '../../stores/jobs'
import { usePaymentsStore } from '../../stores/payments'
import { useAppStore } from '../../stores/app'

const route = useRoute()
const jobsStore = useJobsStore()
const paymentsStore = usePaymentsStore()
const appStore = useAppStore()

const processing = ref(false)

const job = computed(() => jobsStore.currentJob)
const loading = computed(() => jobsStore.loading)
const proposalCount = computed(() => job.value ? Object.keys(job.value.proposals || {}).length : 0)

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  return date.toLocaleDateString()
}

const acceptProposal = async (freelancerId, proposal) => {
  if (!job.value) return

  processing.value = true

  try {
    // Create freelancer data for payment
    const freelancerData = {
      uid: freelancerId,
      name: proposal.freelancerName || 'Freelancer',
      stripeAccountId: 'acct_mock_' + freelancerId // Mock account ID
    }

    // Update job budget to proposal price
    const jobData = {
      ...job.value,
      budget: proposal.price
    }

    // Create payment session
    const result = await paymentsStore.createPaymentSession(jobData, freelancerData)

    if (result.success) {
      // Redirect to checkout (which will go to coming soon page for MVP)
      await paymentsStore.redirectToCheckout(result.session.id)

      // In a real implementation, this would happen after successful payment
      // For MVP, we'll simulate the acceptance
      appStore.showSuccess('Redirecting to payment...')
    } else {
      appStore.showError(result.error || 'Failed to create payment session')
    }
  } catch (error) {
    appStore.showError('Failed to process payment: ' + error.message)
  } finally {
    processing.value = false
  }
}

const rejectProposal = async (freelancerId) => {
  appStore.showInfo('Proposal management coming soon')
}

onMounted(async () => {
  await jobsStore.fetchJob(route.params.id)
})
</script>
