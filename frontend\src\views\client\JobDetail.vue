<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <div v-if="loading" class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
        <p class="mt-2 text-gray-500">Loading job details...</p>
      </div>
      
      <div v-else-if="!job" class="text-center">
        <h1 class="text-2xl font-bold text-gray-900">Job not found</h1>
        <router-link to="/client/dashboard" class="mt-4 text-red-600 hover:text-red-500">
          Back to Dashboard
        </router-link>
      </div>
      
      <div v-else>
        <h1 class="text-3xl font-bold text-gray-900 mb-8">{{ job.title }}</h1>
        <!-- Job details will be implemented in next phase -->
        <div class="bg-white p-6 rounded-lg shadow">
          <p class="text-gray-600">Job details view - Coming soon</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useJobsStore } from '../../stores/jobs'

const route = useRoute()
const jobsStore = useJobsStore()

const job = computed(() => jobsStore.currentJob)
const loading = computed(() => jobsStore.loading)

onMounted(async () => {
  await jobsStore.fetchJob(route.params.id)
})
</script>
