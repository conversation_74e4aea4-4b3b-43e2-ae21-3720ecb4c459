import { loadStripe } from '@stripe/stripe-js'

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY)

// Stripe service class
export class StripeService {
  static async createCheckoutSession(jobData, freelancerData) {
    try {
      // Calculate amounts
      const jobAmount = parseFloat(jobData.budget)
      const platformFee = Math.round(jobAmount * 0.05 * 100) // 5% platform fee in cents
      const freelancerAmount = Math.round((jobAmount - (jobAmount * 0.05)) * 100) // Amount to freelancer in cents
      const totalAmount = Math.round(jobAmount * 100) // Total amount in cents

      // Create checkout session data
      const checkoutData = {
        mode: 'payment',
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: jobData.title,
                description: `Freelance work by ${freelancerData.name}`,
                metadata: {
                  job_id: jobData.id,
                  freelancer_id: freelancerData.uid,
                  client_id: jobData.postedBy
                }
              },
              unit_amount: totalAmount
            },
            quantity: 1
          }
        ],
        payment_intent_data: {
          application_fee_amount: platformFee,
          transfer_data: {
            destination: freelancerData.stripeAccountId
          },
          metadata: {
            job_id: jobData.id,
            freelancer_id: freelancerData.uid,
            client_id: jobData.postedBy,
            platform_fee: platformFee,
            freelancer_amount: freelancerAmount
          }
        },
        success_url: `${window.location.origin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${window.location.origin}/payment/failure`,
        metadata: {
          job_id: jobData.id,
          freelancer_id: freelancerData.uid,
          client_id: jobData.postedBy
        }
      }

      // For MVP, we'll simulate the checkout session creation
      // In production, this would call your backend API
      return this.simulateCheckoutSession(checkoutData)
    } catch (error) {
      console.error('Error creating checkout session:', error)
      throw new Error('Failed to create payment session')
    }
  }

  static async simulateCheckoutSession(checkoutData) {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // For MVP, return a mock session that redirects to a coming soon page
    return {
      id: 'cs_mock_' + Date.now(),
      url: '/coming-soon?feature=stripe-checkout',
      payment_status: 'pending'
    }
  }

  static async redirectToCheckout(sessionId) {
    try {
      const stripe = await stripePromise
      
      // For MVP, redirect to coming soon page
      if (sessionId.startsWith('cs_mock_')) {
        window.location.href = '/coming-soon?feature=stripe-checkout'
        return { error: null }
      }

      // In production, this would redirect to actual Stripe checkout
      const { error } = await stripe.redirectToCheckout({
        sessionId: sessionId
      })

      return { error }
    } catch (error) {
      console.error('Error redirecting to checkout:', error)
      return { error: error.message }
    }
  }

  static async createConnectAccount(userData) {
    try {
      // For MVP, simulate account creation
      await new Promise(resolve => setTimeout(resolve, 1500))

      // In production, this would call your backend to create a Stripe Connect account
      const mockAccountId = 'acct_mock_' + Date.now()
      
      return {
        success: true,
        accountId: mockAccountId,
        onboardingUrl: `/coming-soon?feature=stripe-onboarding&account=${mockAccountId}`
      }
    } catch (error) {
      console.error('Error creating connect account:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  static async getAccountStatus(accountId) {
    try {
      // For MVP, simulate account status check
      await new Promise(resolve => setTimeout(resolve, 500))

      // Mock account status
      return {
        success: true,
        status: 'pending', // 'pending', 'active', 'restricted'
        chargesEnabled: false,
        payoutsEnabled: false,
        requirements: {
          currently_due: ['individual.first_name', 'individual.last_name'],
          eventually_due: ['individual.ssn_last_4'],
          past_due: []
        }
      }
    } catch (error) {
      console.error('Error getting account status:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  static async processRefund(paymentIntentId, amount = null) {
    try {
      // For MVP, simulate refund processing
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        refundId: 're_mock_' + Date.now(),
        amount: amount,
        status: 'succeeded'
      }
    } catch (error) {
      console.error('Error processing refund:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  static calculatePlatformFee(amount, feePercentage = 5) {
    const fee = (amount * feePercentage) / 100
    return {
      originalAmount: amount,
      platformFee: fee,
      freelancerAmount: amount - fee,
      feePercentage
    }
  }

  static formatAmount(amountInCents) {
    return (amountInCents / 100).toFixed(2)
  }

  static async validatePaymentMethod(paymentMethodId) {
    try {
      // For MVP, simulate validation
      await new Promise(resolve => setTimeout(resolve, 300))

      return {
        valid: true,
        last4: '4242',
        brand: 'visa',
        expiryMonth: 12,
        expiryYear: 2025
      }
    } catch (error) {
      console.error('Error validating payment method:', error)
      return {
        valid: false,
        error: error.message
      }
    }
  }

  // Webhook simulation for MVP
  static simulateWebhookEvent(eventType, data) {
    const events = {
      'payment_intent.succeeded': {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: data.paymentIntentId,
            amount: data.amount,
            metadata: data.metadata,
            status: 'succeeded'
          }
        }
      },
      'account.updated': {
        type: 'account.updated',
        data: {
          object: {
            id: data.accountId,
            charges_enabled: data.chargesEnabled,
            payouts_enabled: data.payoutsEnabled
          }
        }
      }
    }

    return events[eventType] || null
  }

  // Helper methods for UI
  static getPaymentStatusColor(status) {
    const colors = {
      'pending': 'yellow',
      'succeeded': 'green',
      'failed': 'red',
      'canceled': 'gray',
      'requires_action': 'blue'
    }
    return colors[status] || 'gray'
  }

  static getPaymentStatusText(status) {
    const texts = {
      'pending': 'Pending',
      'succeeded': 'Completed',
      'failed': 'Failed',
      'canceled': 'Canceled',
      'requires_action': 'Action Required'
    }
    return texts[status] || 'Unknown'
  }
}

export default StripeService
