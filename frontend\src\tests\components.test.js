import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import GamificationPanel from '../components/GamificationPanel.vue'

// Mock stores
const mockGamificationStore = {
  getTierInfo: vi.fn(() => ({
    name: 'Professional',
    color: 'purple',
    perks: ['Featured in search', 'Lower fees']
  })),
  calculateTierProgress: vi.fn(() => ({
    progress: 75,
    xpNeeded: 500,
    nextTier: { name: 'Expert' }
  })),
  getUserBadges: vi.fn(() => [
    { id: 'newcomer', name: 'Newcomer', icon: '🌱', description: 'Welcome to Gignify!' },
    { id: 'first_gig', name: 'First Gig', icon: '🎯', description: 'Completed your first job' }
  ]),
  getRecentAchievements: vi.fn(() => [
    { id: 1, type: 'xp', amount: 100, timestamp: new Date() },
    { id: 2, type: 'badge', badge: { name: 'Rising Star', icon: '⭐' }, timestamp: new Date() }
  ]),
  clearAchievements: vi.fn()
}

vi.mock('../stores/gamification', () => ({
  useGamificationStore: () => mockGamificationStore
}))

// Create router for testing
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/leaderboards', component: { template: '<div>Leaderboards</div>' } }
  ]
})

describe('GamificationPanel', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  const createWrapper = (userData = {}, stats = {}) => {
    const defaultUserData = {
      xp: 2250,
      tier: 3,
      badges: ['newcomer', 'first_gig'],
      ...userData
    }

    const defaultStats = {
      completedJobs: 15,
      averageRating: 4.8,
      ...stats
    }

    return mount(GamificationPanel, {
      props: {
        userData: defaultUserData,
        stats: defaultStats
      },
      global: {
        plugins: [router]
      }
    })
  }

  it('should render user XP and tier information', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('Professional (Tier 3)')
    expect(wrapper.text()).toContain('2250 XP')
  })

  it('should display tier progress bar', () => {
    const wrapper = createWrapper()
    
    const progressBar = wrapper.find('[style*="width: 75%"]')
    expect(progressBar.exists()).toBe(true)
    
    expect(wrapper.text()).toContain('500 XP to Expert')
  })

  it('should show user badges', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('Badges (2)')
    expect(wrapper.text()).toContain('Newcomer')
    expect(wrapper.text()).toContain('First Gig')
  })

  it('should display recent achievements', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('Recent Achievements')
    expect(wrapper.text()).toContain('+100 XP')
    expect(wrapper.text()).toContain('Rising Star')
  })

  it('should show tier perks', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('Tier Perks')
    expect(wrapper.text()).toContain('Featured in search')
    expect(wrapper.text()).toContain('Lower fees')
  })

  it('should display user stats', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('15')
    expect(wrapper.text()).toContain('Jobs Completed')
    expect(wrapper.text()).toContain('4.8/5')
    expect(wrapper.text()).toContain('Avg Rating')
  })

  it('should handle user with no badges', () => {
    const wrapper = createWrapper({ badges: [] })
    
    expect(wrapper.text()).toContain('Badges (0)')
    expect(wrapper.text()).toContain('No badges yet')
    expect(wrapper.text()).toContain('Complete jobs to earn badges!')
  })

  it('should handle max tier user', () => {
    mockGamificationStore.calculateTierProgress.mockReturnValue({
      progress: 100,
      xpNeeded: 0,
      nextTier: null
    })

    const wrapper = createWrapper({ tier: 5 })
    
    expect(wrapper.text()).toContain('Max Tier Reached!')
  })

  it('should format time correctly', () => {
    const now = new Date()
    const oneMinuteAgo = new Date(now.getTime() - 60000)
    const oneHourAgo = new Date(now.getTime() - 3600000)
    
    mockGamificationStore.getRecentAchievements.mockReturnValue([
      { id: 1, type: 'xp', amount: 50, timestamp: oneMinuteAgo },
      { id: 2, type: 'xp', amount: 25, timestamp: oneHourAgo }
    ])

    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('1m ago')
    expect(wrapper.text()).toContain('1h ago')
  })

  it('should show more badges button when user has many badges', async () => {
    const manyBadges = Array.from({ length: 10 }, (_, i) => `badge_${i}`)
    mockGamificationStore.getUserBadges.mockReturnValue(
      manyBadges.map(id => ({ id, name: `Badge ${id}`, icon: '🏆' }))
    )

    const wrapper = createWrapper({ badges: manyBadges })
    
    expect(wrapper.text()).toContain('+4 More')
    
    const showMoreButton = wrapper.find('button:contains("+4 More")')
    await showMoreButton.trigger('click')
    
    expect(wrapper.text()).toContain('Show Less')
  })

  it('should clear achievements on mount', () => {
    createWrapper()
    
    expect(mockGamificationStore.clearAchievements).toHaveBeenCalled()
  })
})

// Integration test for component interaction
describe('GamificationPanel Integration', () => {
  it('should update when user data changes', async () => {
    const wrapper = createWrapper({ xp: 1000, tier: 2 })
    
    expect(wrapper.text()).toContain('1000 XP')
    
    await wrapper.setProps({
      userData: { xp: 1500, tier: 3, badges: [] }
    })
    
    expect(wrapper.text()).toContain('1500 XP')
  })

  it('should handle missing user data gracefully', () => {
    const wrapper = createWrapper({})
    
    // Should not crash and should show default values
    expect(wrapper.text()).toContain('0 XP')
    expect(wrapper.text()).toContain('Tier 1')
  })
})
