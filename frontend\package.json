{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "build:test": "node build-test.js"}, "dependencies": {"vue": "^3.5.17", "pinia": "^2.0.33", "vue-router": "^4.1.6", "firebase": "^10.7.0", "@headlessui/vue": "^1.7.15", "@stripe/stripe-js": "^1.48.0"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/test-utils": "^2.4.0", "autoprefixer": "^10.4.21", "jsdom": "^23.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "vite": "^7.0.4", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0"}}