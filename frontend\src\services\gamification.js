import { updateUserXP } from '../firebase/firestore'
import { doc, updateDoc, arrayUnion } from 'firebase/firestore'
import { db } from '../firebase/config'

// XP Values for different actions
export const XP_VALUES = {
  COMPLETE_JOB: 100,
  REVIEW_5_STAR: 50,
  REVIEW_4_STAR: 30,
  REVIEW_3_STAR: 10,
  REVIEW_2_STAR: 5,
  REVIEW_1_STAR: 0,
  STREAK_5_JOBS: 100,
  STREAK_10_JOBS: 250,
  STREAK_20_JOBS: 500,
  FIRST_JOB: 200,
  CLIENT_FAVORITE: 75,
  PROFILE_COMPLETE: 50,
  PORTFOLIO_UPLOAD: 25,
  SKILL_VERIFICATION: 30
}

// Badge definitions
export const BADGES = {
  NEWCOMER: {
    id: 'newcomer',
    name: 'Newcomer',
    description: 'Welcome to Gignify!',
    icon: '🌱',
    color: 'green',
    requirement: 'Join the platform'
  },
  FIRST_GIG: {
    id: 'first_gig',
    name: 'First Gig',
    description: 'Completed your first job',
    icon: '🎯',
    color: 'blue',
    requirement: 'Complete 1 job'
  },
  RISING_STAR: {
    id: 'rising_star',
    name: 'Rising Star',
    description: 'Consistently high ratings',
    icon: '⭐',
    color: 'yellow',
    requirement: 'Maintain 4.5+ rating with 5+ jobs'
  },
  TOP_PERFORMER: {
    id: 'top_performer',
    name: 'Top Performer',
    description: 'Exceptional work quality',
    icon: '🔥',
    color: 'red',
    requirement: 'Complete 10 jobs with 4.8+ rating'
  },
  CLIENT_FAVORITE: {
    id: 'client_favorite',
    name: 'Client Favorite',
    description: 'Loved by clients',
    icon: '💖',
    color: 'pink',
    requirement: 'Receive 5 client favorite marks'
  },
  SPEED_DEMON: {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Lightning fast delivery',
    icon: '⚡',
    color: 'purple',
    requirement: 'Complete 5 jobs ahead of deadline'
  },
  EXPERT_VERIFIED: {
    id: 'expert_verified',
    name: 'Expert Verified',
    description: 'Skills verified by AI',
    icon: '🧠',
    color: 'indigo',
    requirement: 'Pass skill verification test'
  },
  STREAK_MASTER: {
    id: 'streak_master',
    name: 'Streak Master',
    description: 'Consistent performer',
    icon: '🎯',
    color: 'orange',
    requirement: 'Complete 10 jobs in a row'
  },
  COMMUNITY_CHAMPION: {
    id: 'community_champion',
    name: 'Community Champion',
    description: 'Active community member',
    icon: '👥',
    color: 'teal',
    requirement: 'Help 10 other freelancers'
  },
  MASTER_CRAFTSMAN: {
    id: 'master_craftsman',
    name: 'Master Craftsman',
    description: 'Mastery in your field',
    icon: '👑',
    color: 'gold',
    requirement: 'Complete 50 jobs with 4.9+ rating'
  }
}

// Tier system
export const TIERS = {
  1: { name: 'Newcomer', xpRequired: 0, color: 'gray', perks: ['Basic profile'] },
  2: { name: 'Rising Star', xpRequired: 500, color: 'blue', perks: ['Priority support', 'Profile boost'] },
  3: { name: 'Professional', xpRequired: 1500, color: 'purple', perks: ['Featured in search', 'Lower fees'] },
  4: { name: 'Expert', xpRequired: 3000, color: 'gold', perks: ['Expert badge', 'Premium features'] },
  5: { name: 'Master', xpRequired: 6000, color: 'red', perks: ['Master status', 'Exclusive opportunities'] }
}

// Gamification service class
export class GamificationService {
  static async awardXP(userId, action, metadata = {}) {
    try {
      const xpAmount = XP_VALUES[action] || 0
      if (xpAmount === 0) return { success: false, error: 'Invalid action' }

      const result = await updateUserXP(userId, xpAmount, action.toLowerCase())
      
      if (result.error) {
        return { success: false, error: result.error }
      }

      // Check for new badges
      const newBadges = await this.checkForNewBadges(userId, action, metadata)
      
      // Check for tier promotion
      const tierPromotion = this.checkTierPromotion(result.newXP)

      return {
        success: true,
        xpAwarded: xpAmount,
        newXP: result.newXP,
        newTier: result.newTier,
        newBadges,
        tierPromotion
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  static async checkForNewBadges(userId, action, metadata) {
    const newBadges = []

    try {
      // Check specific action-based badges
      switch (action) {
        case 'COMPLETE_JOB':
          if (metadata.isFirstJob) {
            newBadges.push(BADGES.FIRST_GIG)
          }
          if (metadata.streak >= 10) {
            newBadges.push(BADGES.STREAK_MASTER)
          }
          if (metadata.completedAheadOfDeadline) {
            // Check if this is the 5th early completion
            newBadges.push(BADGES.SPEED_DEMON)
          }
          break

        case 'REVIEW_5_STAR':
          if (metadata.totalJobs >= 5 && metadata.averageRating >= 4.5) {
            newBadges.push(BADGES.RISING_STAR)
          }
          if (metadata.totalJobs >= 10 && metadata.averageRating >= 4.8) {
            newBadges.push(BADGES.TOP_PERFORMER)
          }
          break

        case 'CLIENT_FAVORITE':
          if (metadata.favoriteCount >= 5) {
            newBadges.push(BADGES.CLIENT_FAVORITE)
          }
          break

        case 'SKILL_VERIFICATION':
          newBadges.push(BADGES.EXPERT_VERIFIED)
          break
      }

      // Award badges to user
      if (newBadges.length > 0) {
        await this.awardBadges(userId, newBadges)
      }

      return newBadges
    } catch (error) {
      console.error('Error checking for new badges:', error)
      return []
    }
  }

  static async awardBadges(userId, badges) {
    try {
      const userRef = doc(db, 'users', userId)
      const badgeIds = badges.map(badge => badge.id)
      
      await updateDoc(userRef, {
        badges: arrayUnion(...badgeIds),
        updatedAt: new Date()
      })

      return { success: true }
    } catch (error) {
      console.error('Error awarding badges:', error)
      return { success: false, error: error.message }
    }
  }

  static checkTierPromotion(currentXP) {
    const currentTier = this.calculateTier(currentXP)
    const previousTier = this.calculateTier(currentXP - 100) // Assuming 100 XP was just added
    
    return currentTier > previousTier ? {
      promoted: true,
      newTier: currentTier,
      tierInfo: TIERS[currentTier]
    } : { promoted: false }
  }

  static calculateTier(xp) {
    for (let tier = 5; tier >= 1; tier--) {
      if (xp >= TIERS[tier].xpRequired) {
        return tier
      }
    }
    return 1
  }

  static getTierInfo(tier) {
    return TIERS[tier] || TIERS[1]
  }

  static getBadgeInfo(badgeId) {
    return Object.values(BADGES).find(badge => badge.id === badgeId)
  }

  static calculateProgress(currentXP, currentTier) {
    const currentTierInfo = TIERS[currentTier]
    const nextTierInfo = TIERS[currentTier + 1]
    
    if (!nextTierInfo) {
      return { progress: 100, xpNeeded: 0, nextTier: null }
    }

    const xpInCurrentTier = currentXP - currentTierInfo.xpRequired
    const xpNeededForNextTier = nextTierInfo.xpRequired - currentTierInfo.xpRequired
    const progress = Math.round((xpInCurrentTier / xpNeededForNextTier) * 100)

    return {
      progress: Math.min(progress, 100),
      xpNeeded: nextTierInfo.xpRequired - currentXP,
      nextTier: nextTierInfo
    }
  }

  // Leaderboard calculations
  static async calculateLeaderboardPosition(userId, category = 'global') {
    // This would query the leaderboard and find user's position
    // Implementation depends on how leaderboards are structured
    return { position: null, totalUsers: 0 }
  }

  // Achievement notifications
  static formatAchievementNotification(achievement) {
    switch (achievement.type) {
      case 'xp':
        return {
          title: 'XP Earned!',
          message: `You earned ${achievement.amount} XP`,
          type: 'success'
        }
      case 'badge':
        return {
          title: 'New Badge Unlocked!',
          message: `You earned the "${achievement.badge.name}" badge`,
          type: 'success'
        }
      case 'tier':
        return {
          title: 'Tier Promotion!',
          message: `Welcome to ${achievement.tierInfo.name} tier!`,
          type: 'success'
        }
      default:
        return {
          title: 'Achievement Unlocked!',
          message: 'Great job!',
          type: 'success'
        }
    }
  }
}

export default GamificationService
