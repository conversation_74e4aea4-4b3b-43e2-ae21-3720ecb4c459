{"name": "gignify-frontend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.0", "pinia": "^2.1.0", "vue-router": "^4.2.0", "firebase": "^10.7.0", "@stripe/stripe-js": "^1.48.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}}