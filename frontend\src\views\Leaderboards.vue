<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900">Leaderboards</h1>
        <p class="mt-2 text-lg text-gray-600">Top performers on Gignify</p>
      </div>

      <!-- Category Tabs -->
      <div class="mb-8">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 justify-center">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectedCategory = category.id"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                selectedCategory === category.id
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ category.icon }} {{ category.name }}
            </button>
          </nav>
        </div>
      </div>

      <!-- Leaderboard -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">
            {{ selectedCategory === 'global' ? 'Global' : categories.find(c => c.id === selectedCategory)?.name }} Leaderboard
          </h2>
        </div>

        <div v-if="loading" class="p-6 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
          <p class="mt-2 text-gray-500">Loading leaderboard...</p>
        </div>

        <div v-else-if="currentLeaderboard.length === 0" class="p-6 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No data available</h3>
          <p class="mt-1 text-sm text-gray-500">Check back later for leaderboard updates.</p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="(freelancer, index) in currentLeaderboard"
            :key="freelancer.id"
            class="p-6 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <!-- Rank -->
                <div class="flex-shrink-0">
                  <div :class="[
                    'w-10 h-10 rounded-full flex items-center justify-center text-white font-bold',
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-yellow-600' :
                    'bg-gray-300 text-gray-700'
                  ]">
                    {{ index + 1 }}
                  </div>
                </div>

                <!-- User Info -->
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-medium text-gray-900">{{ freelancer.name }}</h3>
                    <div v-if="freelancer.badges && freelancer.badges.length > 0" class="flex space-x-1">
                      <span
                        v-for="badge in freelancer.badges.slice(0, 3)"
                        :key="badge"
                        class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800"
                      >
                        {{ badge }}
                      </span>
                    </div>
                  </div>
                  <p class="text-sm text-gray-500">{{ freelancer.bio || 'No bio available' }}</p>
                  <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                    <span>Tier {{ freelancer.tier || 1 }}</span>
                    <span>•</span>
                    <span>{{ freelancer.category || 'General' }}</span>
                    <span>•</span>
                    <span>{{ freelancer.location || 'Location not specified' }}</span>
                  </div>
                </div>
              </div>

              <!-- XP and Actions -->
              <div class="flex items-center space-x-4">
                <div class="text-right">
                  <p class="text-2xl font-bold text-red-600">{{ freelancer.xp || 0 }}</p>
                  <p class="text-sm text-gray-500">XP</p>
                </div>
                <router-link
                  :to="`/freelancer/profile/${freelancer.id}`"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-600 bg-red-100 hover:bg-red-200"
                >
                  View Profile
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Stats -->
      <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Total Freelancers</h3>
          <p class="text-3xl font-bold text-blue-600">{{ totalFreelancers }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Active This Week</h3>
          <p class="text-3xl font-bold text-green-600">{{ activeThisWeek }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Top XP This Month</h3>
          <p class="text-3xl font-bold text-yellow-600">{{ topXPThisMonth }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useLeaderboardStore } from '../stores/leaderboard'
import { useJobsStore } from '../stores/jobs'

const leaderboardStore = useLeaderboardStore()
const jobsStore = useJobsStore()

const selectedCategory = ref('global')

const categories = [
  { id: 'global', name: 'Global', icon: '🌍' },
  ...jobsStore.jobCategories
]

const loading = computed(() => leaderboardStore.loading)
const globalLeaderboard = computed(() => leaderboardStore.globalLeaderboard)
const categoryLeaderboards = computed(() => leaderboardStore.categoryLeaderboards)

const currentLeaderboard = computed(() => {
  if (selectedCategory.value === 'global') {
    return globalLeaderboard.value
  }
  return categoryLeaderboards.value[selectedCategory.value] || []
})

// Mock stats - in real app these would come from the store
const totalFreelancers = computed(() => globalLeaderboard.value.length)
const activeThisWeek = computed(() => Math.floor(totalFreelancers.value * 0.3))
const topXPThisMonth = computed(() => {
  const top = globalLeaderboard.value[0]
  return top ? top.xp : 0
})

watch(selectedCategory, async (newCategory) => {
  if (newCategory !== 'global' && !categoryLeaderboards.value[newCategory]) {
    await leaderboardStore.fetchCategoryLeaderboard(newCategory)
  }
})

onMounted(async () => {
  await leaderboardStore.initializeLeaderboards()
})
</script>
