<template>
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Your Progress</h3>
    
    <!-- XP and Tier Progress -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">{{ tierInfo.name }} (Tier {{ currentTier }})</span>
        <span class="text-sm text-gray-500">{{ currentXP }} XP</span>
      </div>
      
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          :class="[
            'h-2 rounded-full transition-all duration-300',
            `bg-${tierInfo.color}-500`
          ]"
          :style="{ width: `${progress.progress}%` }"
        ></div>
      </div>
      
      <div class="flex justify-between text-xs text-gray-500 mt-1">
        <span>{{ tierInfo.name }}</span>
        <span v-if="progress.nextTier">
          {{ progress.xpNeeded }} XP to {{ progress.nextTier.name }}
        </span>
        <span v-else>Max Tier Reached!</span>
      </div>
    </div>

    <!-- Badges -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Badges ({{ userBadges.length }})</h4>
      
      <div v-if="userBadges.length === 0" class="text-center py-4">
        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
        <p class="text-sm text-gray-500 mt-2">No badges yet</p>
        <p class="text-xs text-gray-400">Complete jobs to earn badges!</p>
      </div>

      <div v-else class="grid grid-cols-3 gap-2">
        <div
          v-for="badge in userBadges.slice(0, 6)"
          :key="badge.id"
          :title="badge.description"
          class="flex flex-col items-center p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-help"
        >
          <span class="text-lg">{{ badge.icon }}</span>
          <span class="text-xs text-gray-600 text-center mt-1">{{ badge.name }}</span>
        </div>
      </div>
      
      <div v-if="userBadges.length > 6" class="text-center mt-2">
        <button
          @click="showAllBadges = !showAllBadges"
          class="text-xs text-red-600 hover:text-red-500"
        >
          {{ showAllBadges ? 'Show Less' : `+${userBadges.length - 6} More` }}
        </button>
      </div>
    </div>

    <!-- Recent Achievements -->
    <div v-if="recentAchievements.length > 0" class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Achievements</h4>
      <div class="space-y-2">
        <div
          v-for="achievement in recentAchievements"
          :key="achievement.id"
          class="flex items-center space-x-3 p-2 bg-green-50 rounded-lg"
        >
          <div class="flex-shrink-0">
            <span v-if="achievement.type === 'xp'" class="text-yellow-500">⭐</span>
            <span v-else-if="achievement.type === 'badge'" class="text-lg">{{ achievement.badge.icon }}</span>
            <span v-else-if="achievement.type === 'tier'" class="text-purple-500">👑</span>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900">
              <span v-if="achievement.type === 'xp'">+{{ achievement.amount }} XP</span>
              <span v-else-if="achievement.type === 'badge'">{{ achievement.badge.name }}</span>
              <span v-else-if="achievement.type === 'tier'">{{ achievement.tierInfo.name }} Tier!</span>
            </p>
            <p class="text-xs text-gray-500">{{ formatTime(achievement.timestamp) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tier Perks -->
    <div class="border-t border-gray-200 pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-2">Tier Perks</h4>
      <ul class="text-xs text-gray-600 space-y-1">
        <li v-for="perk in tierInfo.perks" :key="perk" class="flex items-center">
          <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          {{ perk }}
        </li>
      </ul>
    </div>

    <!-- Quick Stats -->
    <div class="border-t border-gray-200 pt-4 mt-4">
      <div class="grid grid-cols-2 gap-4 text-center">
        <div>
          <p class="text-lg font-semibold text-gray-900">{{ stats.completedJobs || 0 }}</p>
          <p class="text-xs text-gray-500">Jobs Completed</p>
        </div>
        <div>
          <p class="text-lg font-semibold text-gray-900">{{ stats.averageRating || 0 }}/5</p>
          <p class="text-xs text-gray-500">Avg Rating</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useGamificationStore } from '../stores/gamification'

const props = defineProps({
  userData: {
    type: Object,
    required: true
  },
  stats: {
    type: Object,
    default: () => ({})
  }
})

const gamificationStore = useGamificationStore()
const showAllBadges = ref(false)

const currentXP = computed(() => props.userData.xp || 0)
const currentTier = computed(() => props.userData.tier || 1)
const tierInfo = computed(() => gamificationStore.getTierInfo(currentTier.value))
const progress = computed(() => gamificationStore.calculateTierProgress(currentXP.value, currentTier.value))
const userBadges = computed(() => gamificationStore.getUserBadges(props.userData.badges))
const recentAchievements = computed(() => gamificationStore.getRecentAchievements(3))

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffInMinutes = Math.floor((now - time) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

onMounted(() => {
  // Clear old achievements on mount
  gamificationStore.clearAchievements()
})
</script>

<style scoped>
/* Dynamic tier colors */
.bg-gray-500 { background-color: #6b7280; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-purple-500 { background-color: #8b5cf6; }
.bg-yellow-500 { background-color: #eab308; }
.bg-red-500 { background-color: #ef4444; }
</style>
